"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[6013],{79986:(e,t,r)=>{r.d(t,{Z:()=>d});r(66845);var n,o=r(50641),i=r(86659),a=r(50669),s=r(1515);const l=(0,r(7865).F4)(n||(n=(0,a.Z)(["\n  50% {\n    color: rgba(0, 0, 0, 0);\n  }\n"]))),c=(0,s.Z)("span",{target:"edlqvik0"})((e=>{let{includeDot:t,shouldBlink:r,theme:n}=e;return{...t?{"&::before":{opacity:1,content:'"\u2022"',animation:"none",color:n.colors.gray,margin:"0 5px"}}:{},...r?{color:n.colors.red,animationName:"".concat(l),animationDuration:"0.5s",animationIterationCount:5}:{}}}),"");var u=r(40864);const d=e=>{let{dirty:t,value:r,maxLength:n,className:a,type:s="single",inForm:l}=e;const d=[],p=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];d.push((0,u.jsx)(c,{includeDot:d.length>0,shouldBlink:t,children:e},d.length))};if(t){const e=l?"submit form":"apply";if("multiline"===s){const t=(0,o.Ge)()?"\u2318":"Ctrl";p("Press ".concat(t,"+Enter to ").concat(e))}else"single"===s&&p("Press Enter to ".concat(e))}return n&&("chat"!==s||t)&&p("".concat(r.length,"/").concat(n),t&&r.length>=n),(0,u.jsx)(i.X7,{"data-testid":"InputInstructions",className:a,children:d})}},46013:(e,t,r)=>{r.r(t),r.d(t,{default:()=>x});var n=r(66845),o=r(25621),i=r(25773),a=r(69),s=n.forwardRef((function(e,t){return n.createElement(a.D,(0,i.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),n.createElement("rect",{width:24,height:24,fill:"none"}),n.createElement("path",{d:"M3 5.51v3.71c0 .46.31.86.76.97L11 12l-7.24 1.81c-.45.11-.76.51-.76.97v3.71c0 .72.73 1.2 1.39.92l15.42-6.49c.82-.34.82-1.5 0-1.84L4.39 4.58C3.73 4.31 3 4.79 3 5.51z"}))}));s.displayName="Send";var l=r(118),c=r(46927),u=r(79986),d=r(27466),p=r(48266),f=r(1515);const h=(0,f.Z)("div",{target:"e1d2x3se4"})((e=>{var t;let{theme:r,width:n}=e;return{borderRadius:r.radii.lg,display:"flex",backgroundColor:null!==(t=r.colors.widgetBackgroundColor)&&void 0!==t?t:r.colors.secondaryBg,width:"".concat(n,"px")}}),""),y=(0,f.Z)("div",{target:"e1d2x3se3"})((e=>{let{theme:t}=e;return{backgroundColor:t.colors.transparent,position:"relative",flexGrow:1,borderRadius:t.radii.lg,display:"flex",alignItems:"center"}}),""),g=(0,f.Z)("button",{target:"e1d2x3se2"})((e=>{let{theme:t,disabled:r,extended:n}=e;const o=(0,d.Iy)(t),[i,a]=o?[t.colors.gray60,t.colors.gray80]:[t.colors.gray80,t.colors.gray40];return{border:"none",backgroundColor:t.colors.transparent,borderTopRightRadius:n?t.radii.none:t.radii.lg,borderTopLeftRadius:n?t.radii.lg:t.radii.none,borderBottomRightRadius:t.radii.lg,display:"inline-flex",alignItems:"center",justifyContent:"center",lineHeight:1,margin:0,padding:t.spacing.sm,color:r?i:a,pointerEvents:"auto","&:focus":{outline:"none"},":focus":{outline:"none"},"&:focus-visible":{backgroundColor:o?t.colors.gray10:t.colors.gray90},"&:hover":{backgroundColor:t.colors.primary,color:t.colors.white},"&:disabled, &:disabled:hover, &:disabled:active":{backgroundColor:t.colors.transparent,borderColor:t.colors.transparent,color:t.colors.gray}}}),""),b=(0,f.Z)("div",{target:"e1d2x3se1"})((()=>({display:"flex",alignItems:"flex-end",height:"100%",position:"absolute",right:"0px",pointerEvents:"none"})),""),m=(0,f.Z)("div",{target:"e1d2x3se0"})({name:"1lm6gnd",styles:"position:absolute;bottom:0px;right:3rem"});var v=r(40864);const x=function(e){let{width:t,element:r,widgetMgr:i}=e;const a=(0,o.u)(),[f,x]=(0,n.useState)(!1),[w,O]=(0,n.useState)(r.default),[j,C]=(0,n.useState)(0),S=(0,n.useRef)(null),P=(0,n.useRef)({minHeight:0,maxHeight:0}),R=()=>{w&&(i.setStringTriggerValue(r,w,{fromUi:!0}),x(!1),O(""),C(0))};(0,n.useEffect)((()=>{if(r.setValue){r.setValue=!1;const e=r.value||"";O(e),x(""!==e)}}),[r]),(0,n.useEffect)((()=>{if(S.current){const{offsetHeight:e}=S.current;P.current.minHeight=e,P.current.maxHeight=6.5*e}}),[S]);const{disabled:k,placeholder:E,maxChars:I}=r,B=(0,d.Iy)(a),{minHeight:T,maxHeight:F}=P.current,Z=B?a.colors.gray70:a.colors.gray80,z=!!(j>0&&S.current)&&Math.abs(j-T)>1;return(0,v.jsx)(h,{className:"stChatInput","data-testid":"stChatInput",width:t,children:(0,v.jsxs)(y,{children:[(0,v.jsx)(l.Z,{inputRef:S,value:w,placeholder:E,onChange:e=>{const{value:t}=e.target,{maxChars:n}=r;0!==n&&t.length>n||(x(""!==t),O(t),C((()=>{let e=0;const{current:t}=S;if(t){const r=t.placeholder;t.placeholder="",t.style.height="auto",e=t.scrollHeight,t.placeholder=r,t.style.height=""}return e})()))},onKeyDown:e=>{const{metaKey:t,ctrlKey:r,shiftKey:n}=e;(e=>{var t;const{keyCode:r,key:n}=e;return("Enter"===n||13===r||10===r)&&!(!0===(null===(t=e.nativeEvent)||void 0===t?void 0:t.isComposing))})(e)&&!n&&!r&&!t&&(e.preventDefault(),R())},"aria-label":E,disabled:k,rows:1,overrides:{Root:{style:{outline:"none",backgroundColor:a.colors.transparent,borderLeftWidth:"1px",borderRightWidth:"1px",borderTopWidth:"1px",borderBottomWidth:"1px",width:"".concat(t,"px")}},InputContainer:{style:{backgroundColor:a.colors.transparent}},Input:{props:{"data-testid":"stChatInputTextArea"},style:{lineHeight:"1.4",backgroundColor:a.colors.transparent,"::placeholder":{color:Z},height:z?"".concat(j+1,"px"):"auto",maxHeight:F?"".concat(F,"px"):"none",paddingRight:"3rem",paddingLeft:a.spacing.sm,paddingBottom:a.spacing.sm,paddingTop:a.spacing.sm}}}}),t>p.A.hideWidgetDetails&&(0,v.jsx)(m,{children:(0,v.jsx)(u.Z,{dirty:f,value:w,maxLength:I,type:"chat",inForm:!1})}),(0,v.jsx)(b,{children:(0,v.jsx)(g,{onClick:R,disabled:!f||k,extended:z,"data-testid":"stChatInputSubmitButton",children:(0,v.jsx)(c.Z,{content:s,size:"xl",color:"inherit"})})})]})})}},118:(e,t,r)=>{r.d(t,{Z:()=>P});var n=r(66845),o=r(80318),i=r(9656),a=r(38254),s=r(80745),l=r(98479);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){d(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function d(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var p=(0,s.zo)("div",(function(e){return u(u({},(0,l.d5)(u(u({$positive:!1},e),{},{$hasIconTrailing:!1}))),{},{width:e.$resize?"fit-content":"100%"})}));p.displayName="StyledTextAreaRoot",p.displayName="StyledTextAreaRoot";var f=(0,s.zo)("div",(function(e){return(0,l.hB)(u({$positive:!1},e))}));f.displayName="StyledTextareaContainer",f.displayName="StyledTextareaContainer";var h=(0,s.zo)("textarea",(function(e){return u(u({},(0,l.Hx)(e)),{},{resize:e.$resize||"none"})}));function y(e){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},y(e)}function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},g.apply(this,arguments)}function b(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,i=[],a=!0,s=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(l){s=!0,o=l}finally{try{a||null==r.return||r.return()}finally{if(s)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return m(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return m(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function v(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function x(e,t){return x=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},x(e,t)}function w(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=j(e);if(t){var o=j(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===y(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return O(e)}(this,r)}}function O(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function j(e){return j=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},j(e)}function C(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}h.displayName="StyledTextarea",h.displayName="StyledTextarea";var S=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&x(e,t)}(c,e);var t,r,s,l=w(c);function c(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,c);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return C(O(e=l.call.apply(l,[this].concat(r))),"state",{isFocused:e.props.autoFocus||!1}),C(O(e),"onFocus",(function(t){e.setState({isFocused:!0}),e.props.onFocus(t)})),C(O(e),"onBlur",(function(t){e.setState({isFocused:!1}),e.props.onBlur(t)})),e}return t=c,(r=[{key:"render",value:function(){var e=this.props.overrides,t=void 0===e?{}:e,r=b((0,o.jb)(t.Root,p),2),s=r[0],l=r[1],c=(0,o.aO)({Input:{component:h},InputContainer:{component:f}},t);return n.createElement(s,g({"data-baseweb":"textarea",$isFocused:this.state.isFocused,$isReadOnly:this.props.readOnly,$disabled:this.props.disabled,$error:this.props.error,$positive:this.props.positive,$required:this.props.required,$resize:this.props.resize},l),n.createElement(i.Z,g({},this.props,{type:a.iB.textarea,overrides:c,onFocus:this.onFocus,onBlur:this.onBlur,resize:this.props.resize})))}}])&&v(t.prototype,r),s&&v(t,s),Object.defineProperty(t,"prototype",{writable:!1}),c}(n.Component);C(S,"defaultProps",{autoFocus:!1,disabled:!1,readOnly:!1,error:!1,name:"",onBlur:function(){},onChange:function(){},onKeyDown:function(){},onKeyPress:function(){},onKeyUp:function(){},onFocus:function(){},overrides:{},placeholder:"",required:!1,rows:3,size:a.NO.default});const P=S}}]);