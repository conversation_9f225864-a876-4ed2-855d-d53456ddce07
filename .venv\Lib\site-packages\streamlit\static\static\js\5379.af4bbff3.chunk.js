"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[5379],{79986:(e,t,r)=>{r.d(t,{Z:()=>p});r(66845);var n,o=r(50641),i=r(86659),s=r(50669),a=r(1515);const l=(0,r(7865).F4)(n||(n=(0,s.Z)(["\n  50% {\n    color: rgba(0, 0, 0, 0);\n  }\n"]))),u=(0,a.Z)("span",{target:"edlqvik0"})((e=>{let{includeDot:t,shouldBlink:r,theme:n}=e;return{...t?{"&::before":{opacity:1,content:'"\u2022"',animation:"none",color:n.colors.gray,margin:"0 5px"}}:{},...r?{color:n.colors.red,animationName:"".concat(l),animationDuration:"0.5s",animationIterationCount:5}:{}}}),"");var c=r(40864);const p=e=>{let{dirty:t,value:r,maxLength:n,className:s,type:a="single",inForm:l}=e;const p=[],d=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];p.push((0,c.jsx)(u,{includeDot:p.length>0,shouldBlink:t,children:e},p.length))};if(t){const e=l?"submit form":"apply";if("multiline"===a){const t=(0,o.Ge)()?"\u2318":"Ctrl";d("Press ".concat(t,"+Enter to ").concat(e))}else"single"===a&&d("Press Enter to ".concat(e))}return n&&("chat"!==a||t)&&d("".concat(r.length,"/").concat(n),t&&r.length>=n),(0,c.jsx)(i.X7,{"data-testid":"InputInstructions",className:s,children:p})}},87814:(e,t,r)=>{r.d(t,{K:()=>o});var n=r(50641);class o{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(e,t,r){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,n.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,r),this.lastWidgetMgr=e,this.lastFormId=t))}disconnect(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}},35379:(e,t,r)=>{r.r(t),r.d(t,{default:()=>y});var n=r(66845),o=r(87814),i=r(118),s=r(79986),a=r(98478),l=r(86659),u=r(8879),c=r(68411),p=r(50641),d=r(48266);const f=(0,r(1515).Z)("div",{target:"ezh4s2r0"})({name:"1om1ktf",styles:"div{border-width:1px;}"});var h=r(40864);class m extends n.PureComponent{constructor(){super(...arguments),this.formClearHelper=new o.K,this.state={dirty:!1,value:this.initialValue},this.commitWidgetValue=e=>{this.props.widgetMgr.setStringValue(this.props.element,this.state.value,e),this.setState({dirty:!1})},this.onFormCleared=()=>{this.setState(((e,t)=>{var r;return{value:null!==(r=t.element.default)&&void 0!==r?r:null}}),(()=>this.commitWidgetValue({fromUi:!0})))},this.onBlur=()=>{this.state.dirty&&this.commitWidgetValue({fromUi:!0})},this.onChange=e=>{const{value:t}=e.target,{element:r}=this.props,{maxChars:n}=r;0!==n&&t.length>n||this.setState({dirty:!0,value:t})},this.isEnterKeyPressed=e=>{var t;const{keyCode:r,key:n}=e;return("Enter"===n||13===r||10===r)&&!(!0===(null===(t=e.nativeEvent)||void 0===t?void 0:t.isComposing))},this.onKeyDown=e=>{const{metaKey:t,ctrlKey:r}=e,{dirty:n}=this.state;if(this.isEnterKeyPressed(e)&&(r||t)&&n){e.preventDefault(),this.commitWidgetValue({fromUi:!0});const{formId:t}=this.props.element;(0,p.$b)({formId:t})&&this.props.widgetMgr.submitForm(this.props.element.formId)}}}get initialValue(){var e;const t=this.props.widgetMgr.getStringValue(this.props.element);return null!==(e=null!==t&&void 0!==t?t:this.props.element.default)&&void 0!==e?e:null}componentDidMount(){this.props.element.setValue?this.updateFromProtobuf():this.commitWidgetValue({fromUi:!1})}componentDidUpdate(){this.maybeUpdateFromProtobuf()}componentWillUnmount(){this.formClearHelper.disconnect()}maybeUpdateFromProtobuf(){const{setValue:e}=this.props.element;e&&this.updateFromProtobuf()}updateFromProtobuf(){const{value:e}=this.props.element;this.props.element.setValue=!1,this.setState({value:null!==e&&void 0!==e?e:null},(()=>{this.commitWidgetValue({fromUi:!1})}))}render(){var e;const{element:t,disabled:r,width:n,widgetMgr:o}=this.props,{value:m,dirty:y}=this.state,b={width:n},{height:v,placeholder:g}=t;return this.formClearHelper.manageFormClearListener(o,t.formId,this.onFormCleared),(0,h.jsxs)("div",{className:"stTextArea","data-testid":"stTextArea",style:b,children:[(0,h.jsx)(a.O,{label:t.label,disabled:r,labelVisibility:(0,p.iF)(null===(e=t.labelVisibility)||void 0===e?void 0:e.value),children:t.help&&(0,h.jsx)(l.dT,{children:(0,h.jsx)(u.Z,{content:t.help,placement:c.u.TOP_RIGHT})})}),(0,h.jsx)(f,{children:(0,h.jsx)(i.Z,{value:null!==m&&void 0!==m?m:"",placeholder:g,onBlur:this.onBlur,onChange:this.onChange,onKeyDown:this.onKeyDown,"aria-label":t.label,disabled:r,overrides:{Input:{style:{lineHeight:"1.4",height:v?"".concat(v,"px"):"",minHeight:"95px",resize:"vertical","::placeholder":{opacity:"0.7"},paddingRight:"1rem",paddingLeft:"1rem",paddingBottom:"1rem",paddingTop:"1rem"}}}})}),n>d.A.hideWidgetDetails&&(0,h.jsx)(s.Z,{dirty:y,value:null!==m&&void 0!==m?m:"",maxLength:t.maxChars,type:"multiline",inForm:(0,p.$b)({formId:t.formId})})]})}}const y=m},118:(e,t,r)=>{r.d(t,{Z:()=>F});var n=r(66845),o=r(80318),i=r(9656),s=r(38254),a=r(80745),l=r(98479);function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){p(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var d=(0,a.zo)("div",(function(e){return c(c({},(0,l.d5)(c(c({$positive:!1},e),{},{$hasIconTrailing:!1}))),{},{width:e.$resize?"fit-content":"100%"})}));d.displayName="StyledTextAreaRoot",d.displayName="StyledTextAreaRoot";var f=(0,a.zo)("div",(function(e){return(0,l.hB)(c({$positive:!1},e))}));f.displayName="StyledTextareaContainer",f.displayName="StyledTextareaContainer";var h=(0,a.zo)("textarea",(function(e){return c(c({},(0,l.Hx)(e)),{},{resize:e.$resize||"none"})}));function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function y(){return y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},y.apply(this,arguments)}function b(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,i=[],s=!0,a=!1;try{for(r=r.call(e);!(s=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);s=!0);}catch(l){a=!0,o=l}finally{try{s||null==r.return||r.return()}finally{if(a)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return v(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return v(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function g(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function O(e,t){return O=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},O(e,t)}function w(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=x(e);if(t){var o=x(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&("object"===m(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return j(e)}(this,r)}}function j(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function x(e){return x=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},x(e)}function P(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}h.displayName="StyledTextarea",h.displayName="StyledTextarea";var C=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&O(e,t)}(u,e);var t,r,a,l=w(u);function u(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return P(j(e=l.call.apply(l,[this].concat(r))),"state",{isFocused:e.props.autoFocus||!1}),P(j(e),"onFocus",(function(t){e.setState({isFocused:!0}),e.props.onFocus(t)})),P(j(e),"onBlur",(function(t){e.setState({isFocused:!1}),e.props.onBlur(t)})),e}return t=u,(r=[{key:"render",value:function(){var e=this.props.overrides,t=void 0===e?{}:e,r=b((0,o.jb)(t.Root,d),2),a=r[0],l=r[1],u=(0,o.aO)({Input:{component:h},InputContainer:{component:f}},t);return n.createElement(a,y({"data-baseweb":"textarea",$isFocused:this.state.isFocused,$isReadOnly:this.props.readOnly,$disabled:this.props.disabled,$error:this.props.error,$positive:this.props.positive,$required:this.props.required,$resize:this.props.resize},l),n.createElement(i.Z,y({},this.props,{type:s.iB.textarea,overrides:u,onFocus:this.onFocus,onBlur:this.onBlur,resize:this.props.resize})))}}])&&g(t.prototype,r),a&&g(t,a),Object.defineProperty(t,"prototype",{writable:!1}),u}(n.Component);P(C,"defaultProps",{autoFocus:!1,disabled:!1,readOnly:!1,error:!1,name:"",onBlur:function(){},onChange:function(){},onKeyDown:function(){},onKeyPress:function(){},onKeyUp:function(){},onFocus:function(){},overrides:{},placeholder:"",required:!1,rows:3,size:s.NO.default});const F=C}}]);