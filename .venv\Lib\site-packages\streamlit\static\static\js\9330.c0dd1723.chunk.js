"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[9330],{69330:(t,e,s)=>{s.r(e),s.d(e,{default:()=>i});var r=s(66845),u=s(40864);function i(t){let{element:e,width:s,endpoints:i}=t;const a=(0,r.useRef)(null);(0,r.useEffect)((()=>{a.current&&(a.current.currentTime=e.startTime)}),[e.startTime]);const n=i.buildMediaURL(e.url);return(0,u.jsx)("audio",{"data-testid":"stAudio",id:"audio",ref:a,controls:!0,src:n,className:"stAudio",style:{width:s}})}}}]);