"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[6692],{2706:(e,t,n)=>{n.r(t),n.d(t,{default:()=>Et});var i=n(66845),o=n(35396),a=n(17330),l=n(57463),r=n(97943),s=n(41342),d=n(17875),c=n(87814),u=n(62622),m=n(16295),h=n(50641),g=n(25621),p=n(34367),f=n(31011),b=n(21e3),v=n(68411),y=n(9003),w=n(81354),x=n(46927),C=n(66694),T=n(1515),E=n(27466);const k=(0,T.Z)("div",{target:"e2wxzia1"})((e=>{let{theme:t,locked:n,target:i}=e;return{padding:"0.5rem 0 0.5rem 0.5rem",position:"absolute",top:n?"-2.4rem":"-1rem",right:t.spacing.none,transition:"none",...!n&&{opacity:0,"&:active, &:focus-visible, &:hover":{transition:"opacity 150ms 100ms, top 100ms 100ms",opacity:1,top:"-2.4rem"},...i&&{["".concat(i,":hover &, ").concat(i,":active &, ").concat(i,":focus-visible &")]:{transition:"opacity 150ms 100ms, top 100ms 100ms",opacity:1,top:"-2.4rem"}}}}}),""),M=(0,T.Z)("div",{target:"e2wxzia0"})((e=>{let{theme:t}=e;return{color:(0,E.Iy)(t)?t.colors.fadedText60:t.colors.bodyText,display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"flex-end",boxShadow:"1px 2px 8px rgba(0, 0, 0, 0.08)",borderRadius:t.radii.lg,backgroundColor:t.colors.lightenedBg05,width:"fit-content",zIndex:t.zIndices.sidebar+1}}),"");var N=n(40864);function R(e){let{label:t,show_label:n,icon:i,onClick:o}=e;const a=(0,g.u)(),l=n?t:"";return(0,N.jsx)("div",{"data-testid":"stElementToolbarButton",children:(0,N.jsx)(v.Z,{content:(0,N.jsx)(b.ZP,{source:t,allowHTML:!1,style:{fontSize:a.fontSizes.sm}}),placement:v.u.TOP,onMouseEnterDelay:1e3,inline:!0,children:(0,N.jsxs)(y.ZP,{onClick:e=>{o&&o(),e.stopPropagation()},kind:w.nW.ELEMENT_TOOLBAR,children:[i&&(0,N.jsx)(x.Z,{content:i,size:"md",testid:"stElementToolbarButtonIcon"}),l&&(0,N.jsx)("span",{children:l})]})})})}const S=e=>{let{onExpand:t,onCollapse:n,isFullScreen:o,locked:a,children:l,target:r}=e;const{libConfig:s}=i.useContext(C.E);return(0,N.jsx)(k,{className:"stElementToolbar","data-testid":"stElementToolbar",locked:a||o,target:r,children:(0,N.jsxs)(M,{children:[l,t&&!s.disableFullscreenMode&&!o&&(0,N.jsx)(R,{label:"Fullscreen",icon:p.i,onClick:()=>t()}),n&&!s.disableFullscreenMode&&o&&(0,N.jsx)(R,{label:"Close fullscreen",icon:f.m,onClick:()=>n()})]})})};var _=n(38145),O=n.n(_),I=n(96825),D=n.n(I),H=n(29724),A=n.n(H),z=n(52347),V=n(53608),F=n.n(V);n(87717),n(55842);const j=["true","t","yes","y","on","1"],L=["false","f","no","n","off","0"];function W(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e="\u26a0\ufe0f ".concat(e),{kind:o.p6.Text,readonly:!0,allowOverlay:!0,data:e+(t?"\n\n".concat(t,"\n"):""),displayData:e,isError:!0}}function B(e){return e.hasOwnProperty("isError")&&e.isError}function P(e){return e.hasOwnProperty("isMissingValue")&&e.isMissingValue}function Y(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?{kind:o.p6.Loading,allowOverlay:!1,isMissingValue:!0}:{kind:o.p6.Loading,allowOverlay:!1}}function Z(e,t){const n=t?"faded":"normal";return{kind:o.p6.Text,data:"",displayData:"",allowOverlay:!0,readonly:e,style:n}}function q(e){return{id:e.id,title:e.title,hasMenu:!1,themeOverride:e.themeOverride,icon:e.icon,...e.isStretched&&{grow:e.isIndex?1:3},...e.width&&{width:e.width}}}function J(e,t){return(0,h.le)(e)?t||{}:(0,h.le)(t)?e||{}:D()(e,t)}function K(e){if((0,h.le)(e))return[];if("number"===typeof e||"boolean"===typeof e)return[e];if("string"===typeof e){if(""===e)return[];if(!e.trim().startsWith("[")||!e.trim().endsWith("]"))return e.split(",");try{return JSON.parse(e)}catch(t){return[e]}}try{const t=JSON.parse(JSON.stringify(e,((e,t)=>"bigint"===typeof t?Number(t):t)));return Array.isArray(t)?t.map((e=>["string","number","boolean","null"].includes(typeof e)?e:X(e))):[X(t)]}catch(t){return[X(e)]}}function X(e){try{try{return O()(e)}catch(t){return JSON.stringify(e,((e,t)=>"bigint"===typeof t?Number(t):t))}}catch(t){return"[".concat(typeof e,"]")}}function G(e){if((0,h.le)(e))return null;if("boolean"===typeof e)return e;const t=X(e).toLowerCase().trim();return""===t?null:!!j.includes(t)||!L.includes(t)&&void 0}function U(e){if((0,h.le)(e))return null;if(Array.isArray(e))return NaN;if("string"===typeof e){if(0===e.trim().length)return null;try{const t=A().unformat(e.trim());if((0,h.bb)(t))return t}catch(t){}}else if(e instanceof Int32Array)return Number(e[0]);return Number(e)}function Q(e,t,n){return Number.isNaN(e)||!Number.isFinite(e)?"":(0,h.le)(t)||""===t?(0===n&&(e=Math.round(e)),A()(e).format((0,h.bb)(n)?"0,0.".concat("0".repeat(n)):"0,0.[0000]")):"percent"===t?new Intl.NumberFormat(void 0,{style:"percent",minimumFractionDigits:2,maximumFractionDigits:2}).format(e):["compact","scientific","engineering"].includes(t)?new Intl.NumberFormat(void 0,{notation:t}).format(e):"duration[ns]"===t?F().duration(e/1e6,"milliseconds").humanize():(0,z.sprintf)(t,e)}function $(e,t){return"locale"===t?new Intl.DateTimeFormat(void 0,{dateStyle:"medium",timeStyle:"medium"}).format(e.toDate()):"distance"===t?e.fromNow():"relative"===t?e.calendar():e.format(t)}function ee(e){if((0,h.le)(e))return null;if(e instanceof Date)return isNaN(e.getTime())?void 0:e;if("string"===typeof e&&0===e.trim().length)return null;try{const t=Number(e);if(!isNaN(t)){let e=t;t>=10**18?e=t/1e3**3:t>=10**15?e=t/1e6:t>=10**12&&(e=t/1e3);const n=F().unix(e).utc();if(n.isValid())return n.toDate()}if("string"===typeof e){const t=F().utc(e);if(t.isValid())return t.toDate();const n=F().utc(e,[F().HTML5_FMT.TIME_MS,F().HTML5_FMT.TIME_SECONDS,F().HTML5_FMT.TIME]);if(n.isValid())return n.toDate()}}catch(t){return}}function te(e){if(e%1===0)return 0;let t=e.toString();return-1!==t.indexOf("e")&&(t=e.toLocaleString("fullwide",{useGrouping:!1,maximumFractionDigits:20})),-1===t.indexOf(".")?0:t.split(".")[1].length}const ne=new RegExp(/(\r\n|\n|\r)/gm);function ie(e){return-1!==e.indexOf("\n")?e.replace(ne," "):e}var oe=n(23849),ae=n(72789);function le(e){const t={kind:o.p6.Text,data:"",displayData:"",allowOverlay:!0,contentAlignment:e.contentAlignment,readonly:!0,style:e.isIndex?"faded":"normal"};return{...e,kind:"object",sortMode:"default",isEditable:!1,getCell(e){try{const n=(0,h.bb)(e)?X(e):null,i=(0,h.bb)(n)?ie(n):"";return{...t,data:n,displayData:i,isMissingValue:(0,h.le)(e)}}catch(n){return W(X(e),"The value cannot be interpreted as a string. Error: ".concat(n))}},getCellValue:e=>void 0===e.data?null:e.data}}le.isEditableType=!1;const re=le;function se(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(l){n="Invalid validate regex: ".concat(t.validate,".\nError: ").concat(l)}const i={kind:o.p6.Text,data:"",displayData:"",allowOverlay:!0,contentAlignment:e.contentAlignment,readonly:!e.isEditable,style:e.isIndex?"faded":"normal"},a=i=>{if((0,h.le)(i))return!e.isRequired;let o=X(i),a=!1;return t.max_chars&&o.length>t.max_chars&&(o=o.slice(0,t.max_chars),a=!0),!(n instanceof RegExp&&!1===n.test(o))&&(!a||o)};return{...e,kind:"text",sortMode:"default",validateInput:a,getCell(e,t){if("string"===typeof n)return W(X(e),n);if(t){const t=a(e);if(!1===t)return W(X(e),"Invalid input.");"string"===typeof t&&(e=t)}try{const t=(0,h.bb)(e)?X(e):null,n=(0,h.bb)(t)?ie(t):"";return{...i,isMissingValue:(0,h.le)(t),data:t,displayData:n}}catch(l){return W("Incompatible value","The value cannot be interpreted as string. Error: ".concat(l))}},getCellValue:e=>void 0===e.data?null:e.data}}se.isEditableType=!0;const de=se;function ce(e,t){return e=t.startsWith("+")||t.startsWith("-")?e.utcOffset(t,!1):e.tz(t)}function ue(e,t,n,i,a,l,r){var s;const d=J({format:n,step:i,timezone:r},t.columnTypeOptions);let c,u,m;if((0,h.bb)(d.timezone))try{var g;c=(null===(g=ce(F()(),d.timezone))||void 0===g?void 0:g.utcOffset())||void 0}catch(b){}(0,h.bb)(d.min_value)&&(u=ee(d.min_value)||void 0),(0,h.bb)(d.max_value)&&(m=ee(d.max_value)||void 0);const p={kind:o.p6.Custom,allowOverlay:!0,copyData:"",readonly:!t.isEditable,contentAlign:t.contentAlignment,style:t.isIndex?"faded":"normal",data:{kind:"date-picker-cell",date:void 0,displayDate:"",step:(null===(s=d.step)||void 0===s?void 0:s.toString())||"1",format:a,min:u,max:m}},f=e=>{const n=ee(e);return null===n?!t.isRequired:void 0!==n&&(!((0,h.bb)(u)&&l(n)<l(u))&&!((0,h.bb)(m)&&l(n)>l(m)))};return{...t,kind:e,sortMode:"default",validateInput:f,getCell(e,t){if(!0===t){const t=f(e);if(!1===t)return W(X(e),"Invalid input.");t instanceof Date&&(e=t)}const i=ee(e);let o="",a="",l=c;if(void 0===i)return W(X(e),"The value cannot be interpreted as a datetime object.");if(null!==i){let e=F().utc(i);if(!e.isValid())return W(X(i),"This should never happen. Please report this bug. \nError: ".concat(e.toString()));if(d.timezone){try{e=ce(e,d.timezone)}catch(b){return W(e.toISOString(),"Failed to adjust to the provided timezone: ".concat(d.timezone,". \nError: ").concat(b))}l=e.utcOffset()}try{a=$(e,d.format||n)}catch(b){return W(e.toISOString(),"Failed to format the date for rendering with: ".concat(d.format,". \nError: ").concat(b))}o=$(e,n)}return{...p,copyData:o,isMissingValue:(0,h.le)(i),data:{...p.data,date:i,displayDate:a,timezoneOffset:l}}},getCellValue(e){var t;return(0,h.le)(null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.date)?null:l(e.data.date)}}}function me(e){var t,n,i,o,a;let l="YYYY-MM-DD HH:mm:ss";(null===(t=e.columnTypeOptions)||void 0===t?void 0:t.step)>=60?l="YYYY-MM-DD HH:mm":(null===(n=e.columnTypeOptions)||void 0===n?void 0:n.step)<1&&(l="YYYY-MM-DD HH:mm:ss.SSS");const r=null===(i=e.arrowType)||void 0===i||null===(o=i.meta)||void 0===o?void 0:o.timezone,s=(0,h.bb)(r)||(0,h.bb)(null===e||void 0===e||null===(a=e.columnTypeOptions)||void 0===a?void 0:a.timezone);return ue("datetime",e,s?l+"Z":l,1,"datetime-local",(e=>s?e.toISOString():e.toISOString().replace("Z","")),r)}function he(e){var t,n;let i="HH:mm:ss";return(null===(t=e.columnTypeOptions)||void 0===t?void 0:t.step)>=60?i="HH:mm":(null===(n=e.columnTypeOptions)||void 0===n?void 0:n.step)<1&&(i="HH:mm:ss.SSS"),ue("time",e,i,1,"time",(e=>e.toISOString().split("T")[1].replace("Z","")))}function ge(e){return ue("date",e,"YYYY-MM-DD",1,"date",(e=>e.toISOString().split("T")[0]))}function pe(e){const t={kind:o.p6.Boolean,data:!1,allowOverlay:!1,contentAlign:e.contentAlignment,readonly:!e.isEditable,style:e.isIndex?"faded":"normal"};return{...e,kind:"checkbox",sortMode:"default",getCell(e){let n=null;return n=G(e),void 0===n?W(X(e),"The value cannot be interpreted as boolean."):{...t,data:n,isMissingValue:(0,h.le)(n)}},getCellValue:e=>void 0===e.data?null:e.data}}me.isEditableType=!0,he.isEditableType=!0,ge.isEditableType=!0,pe.isEditableType=!0;const fe=pe;function be(e){return e.startsWith("int")&&!e.startsWith("interval")||"range"===e||e.startsWith("uint")}function ve(e){const t=ae.fu.getTypeName(e.arrowType),n=J({step:be(t)?1:void 0,min_value:t.startsWith("uint")?0:void 0,format:"timedelta64[ns]"===t?"duration[ns]":void 0},e.columnTypeOptions),i=(0,h.le)(n.min_value)||n.min_value<0,a=(0,h.bb)(n.step)&&!Number.isNaN(n.step)?te(n.step):void 0,l={kind:o.p6.Number,data:void 0,displayData:"",readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment||"right",style:e.isIndex?"faded":"normal",allowNegative:i,fixedDecimals:a},r=t=>{let i=U(t);if((0,h.le)(i))return!e.isRequired;if(Number.isNaN(i))return!1;let o=!1;return(0,h.bb)(n.max_value)&&i>n.max_value&&(i=n.max_value,o=!0),!((0,h.bb)(n.min_value)&&i<n.min_value)&&(!o||i)};return{...e,kind:"number",sortMode:"smart",validateInput:r,getCell(e,t){if(!0===t){const t=r(e);if(!1===t)return W(X(e),"Invalid input.");"number"===typeof t&&(e=t)}let i=U(e),o="";if((0,h.bb)(i)){if(Number.isNaN(i))return W(X(e),"The value cannot be interpreted as a number.");if((0,h.bb)(a)&&(s=i,i=0===(d=a)?Math.trunc(s):Math.trunc(s*10**d)/10**d),Number.isInteger(i)&&!Number.isSafeInteger(i))return W(X(e),"The value is larger than the maximum supported integer values in number columns (2^53).");try{o=Q(i,n.format,a)}catch(c){return W(X(i),(0,h.bb)(n.format)?"Failed to format the number based on the provided format configuration: (".concat(n.format,"). Error: ").concat(c):"Failed to format the number. Error: ".concat(c))}}var s,d;return{...l,data:i,displayData:o,isMissingValue:(0,h.le)(i)}},getCellValue:e=>void 0===e.data?null:e.data}}ve.isEditableType=!0;const ye=ve;function we(e){let t="string";const n=J({options:"bool"===ae.fu.getTypeName(e.arrowType)?[!0,!1]:[]},e.columnTypeOptions),i=new Set(n.options.map((e=>typeof e)));1===i.size&&(i.has("number")||i.has("bigint")?t="number":i.has("boolean")&&(t="boolean"));const a={kind:o.p6.Custom,allowOverlay:!0,copyData:"",contentAlign:e.contentAlignment,readonly:!e.isEditable,data:{kind:"dropdown-cell",allowedValues:[...!0!==e.isRequired?[null]:[],...n.options.filter((e=>null!==e&&""!==e)).map((e=>X(e)))],value:"",readonly:!e.isEditable}};return{...e,kind:"selectbox",sortMode:"default",getCell(e,t){let n=null;return(0,h.bb)(e)&&""!==e&&(n=X(e)),t&&!a.data.allowedValues.includes(n)?W(X(n),"The value is not part of the allowed options."):{...a,isMissingValue:null===n,copyData:n||"",data:{...a.data,value:n}}},getCellValue(e){var n,i,o,a,l,r,s;return(0,h.le)(null===(n=e.data)||void 0===n?void 0:n.value)||""===(null===(i=e.data)||void 0===i?void 0:i.value)?null:"number"===t?null!==(a=U(null===(l=e.data)||void 0===l?void 0:l.value))&&void 0!==a?a:null:"boolean"===t?null!==(r=G(null===(s=e.data)||void 0===s?void 0:s.value))&&void 0!==r?r:null:null===(o=e.data)||void 0===o?void 0:o.value}}}we.isEditableType=!0;const xe=we;function Ce(e){const t={kind:o.p6.Bubble,data:[],allowOverlay:!0,contentAlign:e.contentAlignment,style:e.isIndex?"faded":"normal"};return{...e,kind:"list",sortMode:"default",isEditable:!1,getCell(e){const n=(0,h.le)(e)?[]:K(e);return{...t,data:n,isMissingValue:(0,h.le)(e),copyData:(0,h.le)(e)?"":X(n.map((e=>"string"===typeof e&&e.includes(",")?e.replace(/,/g," "):e)))}},getCellValue:e=>(0,h.le)(e.data)||P(e)?null:e.data}}Ce.isEditableType=!1;const Te=Ce;function Ee(e,t,n){const i=new RegExp("".concat(e,"[,\\s].*{(?:[^}]*[\\s;]{1})?").concat(t,":\\s*([^;}]+)[;]?.*}"),"gm");n=n.replace(/{/g," {");const o=i.exec(n);if(o)return o[1].trim()}function ke(e,t){const n=e.types.index[t],i=e.indexNames[t];let o=!0;return"range"===ae.fu.getTypeName(n)&&(o=!1),{id:"index-".concat(t),name:i,title:i,isEditable:o,arrowType:n,isIndex:!0,isHidden:!1}}function Me(e,t){const n=e.columns[0][t];let i,o=e.types.data[t];if((0,h.le)(o)&&(o={meta:null,numpy_type:"object",pandas_type:"object"}),"categorical"===ae.fu.getTypeName(o)){const n=e.getCategoricalOptions(t);(0,h.bb)(n)&&(i={options:n})}return{id:"column-".concat(n,"-").concat(t),name:n,title:n,isEditable:!0,arrowType:o,columnTypeOptions:i,isIndex:!1,isHidden:!1}}function Ne(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;const i=e.arrowType?ae.fu.getTypeName(e.arrowType):null;let a;if("object"===e.kind)a=e.getCell((0,h.bb)(t.content)?ie(ae.fu.format(t.content,t.contentType,t.field)):null);else if(["time","date","datetime"].includes(e.kind)&&(0,h.bb)(t.content)&&("number"===typeof t.content||"bigint"===typeof t.content)){var l,r;let n;var s,d,c;if("time"===i&&(0,h.bb)(null===(l=t.field)||void 0===l||null===(r=l.type)||void 0===r?void 0:r.unit))n=F().unix(ae.fu.convertToSeconds(t.content,null!==(s=null===(d=t.field)||void 0===d||null===(c=d.type)||void 0===c?void 0:c.unit)&&void 0!==s?s:0)).utc().toDate();else n=F().utc(Number(t.content)).toDate();a=e.getCell(n)}else if("decimal"===i){const n=(0,h.le)(t.content)?null:ae.fu.format(t.content,t.contentType,t.field);a=e.getCell(n)}else a=e.getCell(t.content);if(B(a))return a;if(!e.isEditable){if((0,h.bb)(t.displayContent)){var u,m;const e=ie(t.displayContent);a.kind===o.p6.Text||a.kind===o.p6.Number?a={...a,displayData:e}:a.kind===o.p6.Custom&&"date-picker-cell"===(null===(u=a.data)||void 0===u?void 0:u.kind)?a={...a,data:{...a.data,displayDate:e}}:a.kind===o.p6.Custom&&"link-cell"===(null===(m=a.data)||void 0===m?void 0:m.kind)&&(a={...a,data:{...a.data,displayText:e}})}n&&t.cssId&&(a=function(e,t,n){const i={},o=Ee(t,"color",n);o&&(i.textDark=o);const a=Ee(t,"background-color",n);return a&&(i.bgCell=a),"yellow"===a&&void 0===o&&(i.textDark="#31333F"),i?{...e,themeOverride:i}:e}(a,t.cssId,n))}return a}function Re(e){const t=e.columnTypeOptions||{};let n,i;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(r){n="Invalid validate regex: ".concat(t.validate,".\nError: ").concat(r)}if(!(0,h.le)(t.display_text)&&t.display_text.includes("(")&&t.display_text.includes(")"))try{i=new RegExp(t.display_text,"us")}catch(r){i=void 0}const a={kind:o.p6.Custom,readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment,style:e.isIndex?"faded":"normal",data:{kind:"link-cell",href:"",displayText:""},copyData:""},l=i=>{if((0,h.le)(i))return!e.isRequired;const o=X(i);return!(t.max_chars&&o.length>t.max_chars)&&!(n instanceof RegExp&&!1===n.test(o))};return{...e,kind:"link",sortMode:"default",validateInput:l,getCell(e,o){if((0,h.le)(e))return{...a,data:{...a.data,href:null},isMissingValue:!0};const s=e;if("string"===typeof n)return W(X(s),n);if(o){if(!1===l(s))return W(X(s),"Invalid input.")}let d="";return s&&(d=void 0!==i?function(e,t){if((0,h.le)(t))return"";try{const n=t.match(e);return n&&void 0!==n[1]?n[1]:t}catch(r){return t}}(i,s):t.display_text||s),{...a,data:{kind:"link-cell",href:s,displayText:d},copyData:s,cursor:"pointer",isMissingValue:(0,h.le)(s)}},getCellValue(e){var t;return(0,h.le)(null===(t=e.data)||void 0===t?void 0:t.href)?null:e.data.href}}}Re.isEditableType=!0;const Se=Re;function _e(e){const t={kind:o.p6.Image,data:[],displayData:[],allowAdd:!1,allowOverlay:!0,contentAlign:e.contentAlignment||"center",style:e.isIndex?"faded":"normal"};return{...e,kind:"image",sortMode:"default",isEditable:!1,getCell(e){const n=(0,h.bb)(e)?[X(e)]:[];return{...t,data:n,isMissingValue:!(0,h.bb)(e),displayData:n}},getCellValue:e=>void 0===e.data||0===e.data.length?null:e.data[0]}}_e.isEditableType=!1;const Oe=_e;function Ie(e){const t=be(ae.fu.getTypeName(e.arrowType)),n=J({min_value:0,max_value:t?100:1,step:t?1:.01,format:t?"%3d%%":"percent"},e.columnTypeOptions);let i;try{i=Q(n.max_value,n.format)}catch(r){i=X(n.max_value)}const a=(0,h.le)(n.step)||Number.isNaN(n.step)?void 0:te(n.step),l={kind:o.p6.Custom,allowOverlay:!1,copyData:"",contentAlign:e.contentAlignment,data:{kind:"range-cell",min:n.min_value,max:n.max_value,step:n.step,value:n.min_value,label:String(n.min_value),measureLabel:i,readonly:!0}};return{...e,kind:"progress",sortMode:"smart",isEditable:!1,getCell(e){if((0,h.le)(e))return Y();if((0,h.le)(n.min_value)||(0,h.le)(n.max_value)||Number.isNaN(n.min_value)||Number.isNaN(n.max_value)||n.min_value>=n.max_value)return W("Invalid min/max parameters","The min_value (".concat(n.min_value,") and max_value (").concat(n.max_value,") parameters must be valid numbers."));if((0,h.le)(n.step)||Number.isNaN(n.step))return W("Invalid step parameter","The step parameter (".concat(n.step,") must be a valid number."));const t=U(e);if(Number.isNaN(t)||(0,h.le)(t))return W(X(e),"The value cannot be interpreted as a number.");if(Number.isInteger(t)&&!Number.isSafeInteger(t))return W(X(e),"The value is larger than the maximum supported integer values in number columns (2^53).");let i="";try{i=Q(t,n.format,a)}catch(r){return W(X(t),(0,h.bb)(n.format)?"Failed to format the number based on the provided format configuration: (".concat(n.format,"). Error: ").concat(r):"Failed to format the number. Error: ".concat(r))}const o=Math.min(n.max_value,Math.max(n.min_value,t));return{...l,isMissingValue:(0,h.le)(e),copyData:String(t),data:{...l.data,value:o,label:i}}},getCellValue(e){var t,n;return e.kind===o.p6.Loading||void 0===(null===(t=e.data)||void 0===t?void 0:t.value)?null:null===(n=e.data)||void 0===n?void 0:n.value}}}Ie.isEditableType=!1;const De=Ie;function He(e,t,n){const i=J({y_min:0,y_max:1},t.columnTypeOptions),a={kind:o.p6.Custom,allowOverlay:!1,copyData:"",contentAlign:t.contentAlignment,data:{kind:"sparkline-cell",values:[],displayValues:[],graphKind:n,yAxis:[i.y_min,i.y_max]}};return{...t,kind:e,sortMode:"default",isEditable:!1,getCell(e){if((0,h.le)(i.y_min)||(0,h.le)(i.y_max)||Number.isNaN(i.y_min)||Number.isNaN(i.y_max)||i.y_min>=i.y_max)return W("Invalid min/max y-axis configuration","The y_min (".concat(i.y_min,") and y_max (").concat(i.y_max,") configuration options must be valid numbers."));if((0,h.le)(e))return Y();const t=K(e),o=[];let l=[];if(0===t.length)return Y();let r=Number.MIN_SAFE_INTEGER,s=Number.MAX_SAFE_INTEGER;for(let n=0;n<t.length;n++){const e=U(t[n]);if(Number.isNaN(e)||(0,h.le)(e))return W(X(t),"The value cannot be interpreted as a numeric array. ".concat(X(e)," is not a number."));e>r&&(r=e),e<s&&(s=e),o.push(e)}return"line"===n&&o.length<=2?Y():(l=o.length>0&&(r>i.y_max||s<i.y_min)?o.map((e=>r-s===0?r>(i.y_max||1)?i.y_max||1:i.y_min||0:((i.y_max||1)-(i.y_min||0))*((e-s)/(r-s))+(i.y_min||0))):o,{...a,copyData:o.join(","),data:{...a.data,values:l,displayValues:o.map((e=>Q(e)))},isMissingValue:(0,h.le)(e)})},getCellValue(e){var t,n;return e.kind===o.p6.Loading||void 0===(null===(t=e.data)||void 0===t?void 0:t.values)?null:null===(n=e.data)||void 0===n?void 0:n.values}}}function Ae(e){return He("line_chart",e,"line")}function ze(e){return He("bar_chart",e,"bar")}Ae.isEditableType=!1,ze.isEditableType=!1;const Ve=(0,T.Z)("div",{target:"ex7xuzc0"})({name:"iatp2c",styles:"display:flex;flex-grow:1;align-items:center;min-height:21px;.gdg-link-area{flex-grow:1;flex-shrink:1;cursor:pointer;margin-right:8px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:var(--gdg-link-color);text-decoration:underline!important;padding-bottom:3px;}"}),Fe=e=>{const{uri:t,onChange:n,readonly:i,validatedSelection:a,preview:l}=e;return i?(0,N.jsx)(Ve,{children:(0,N.jsx)("a",{"data-testid":"stLinkCell",className:"gdg-link-area",href:null!==t&&void 0!==t?t:"",target:"_blank",rel:"noopener noreferrer",children:l})}):(0,N.jsx)(o.t5,{validatedSelection:a,highlight:!0,autoFocus:!0,value:null!==t&&void 0!==t?t:"",onChange:n})};const je={draw:(e,t)=>{const{ctx:n,rect:i,theme:a,hoverX:l=-100}=e,{href:r,displayText:s}=t.data;if((0,h.le)(r))return;const d=s||r,c=a.cellHorizontalPadding,u=i.x+c,m=i.x+l,g="".concat(a.baseFontStyle," ").concat(a.fontFamily);n.font=g;const p=(0,o.aX)(n,g),f=i.y+i.height/2+p;if(m>i.x&&m<i.x+i.width){const e=(0,o.P7)(d,n,g);n.moveTo(u,Math.floor(f+5)+.5),n.lineTo(u+e.width,Math.floor(f+5)+.5),n.strokeStyle=a.linkColor,n.stroke()}return n.fillStyle=a.linkColor,n.fillText(d,u,f),n.closePath(),!0},isMatch:e=>"link-cell"===e.data.kind,kind:o.p6.Custom,measure:(e,t,n)=>{const{href:i,displayText:o}=t.data;return(0,h.le)(i)?0:e.measureText(o||i).width+2*n.cellHorizontalPadding},needsHover:!0,needsHoverPosition:!0,onSelect:e=>{const t=function(e){var t;const n=document.createElement("canvas").getContext("2d",{alpha:!1});if(null===n)return;const{posX:i,bounds:o,cell:a,theme:l}=e,r="".concat(l.baseFontStyle," ").concat(l.fontFamily);n.font=r;const{href:s,displayText:d}=a.data,c=o.x+i,u=n.measureText(null!==(t=d||s)&&void 0!==t?t:"").width,m=o.x+l.cellHorizontalPadding;return c>m&&c<m+u?s:void 0}(e);(0,h.le)(t)||window.open(t,"_blank","noopener,noreferrer")},onDelete:e=>({...e,data:{...e.data,displayText:"",href:""}}),provideEditor:()=>e=>{var t;const{onChange:n,value:i,validatedSelection:o}=e,{href:a,displayText:l}=i.data;return(0,N.jsx)(Fe,{uri:i.data.href,preview:null!==(t=l||a)&&void 0!==t?t:"",validatedSelection:o,readonly:!0===i.readonly,onChange:e=>n({...i,copyData:e.target.value,data:{...i.data,href:e.target.value}})})},onPaste:(e,t)=>e===t.href?void 0:{...t,href:e}},Le=new Map(Object.entries({object:re,text:de,checkbox:fe,selectbox:xe,list:Te,number:ye,link:Se,datetime:me,date:ge,time:he,line_chart:Ae,bar_chart:ze,image:Oe,progress:De})),We=[je],Be="_index",Pe="_pos:",Ye={small:75,medium:200,large:400};function Ze(e){if(!(0,h.le)(e))return"number"===typeof e?e:e in Ye?Ye[e]:void 0}function qe(e,t){if(!t)return e;let n;return t.has(e.name)&&e.name!==Be?n=t.get(e.name):t.has("".concat(Pe).concat(e.indexNumber))?n=t.get("".concat(Pe).concat(e.indexNumber)):e.isIndex&&t.has(Be)&&(n=t.get(Be)),n?D()({...e},{title:n.label,width:Ze(n.width),isEditable:(0,h.bb)(n.disabled)?!n.disabled:void 0,isHidden:n.hidden,isRequired:n.required,columnTypeOptions:n.type_config,contentAlignment:n.alignment,defaultValue:n.default,help:n.help}):e}function Je(e){var t;const n=null===(t=e.columnTypeOptions)||void 0===t?void 0:t.type;let i;return(0,h.bb)(n)&&(Le.has(n)?i=Le.get(n):(0,oe.KE)("Unknown column type configured in column configuration: ".concat(n))),(0,h.le)(i)&&(i=function(e){let t=e?ae.fu.getTypeName(e):null;return t?(t=t.toLowerCase().trim(),["unicode","empty"].includes(t)?de:["datetime","datetimetz"].includes(t)?me:"time"===t?he:"date"===t?ge:["object","bytes"].includes(t)?re:["bool"].includes(t)?fe:["int8","int16","int32","int64","uint8","uint16","uint32","uint64","float16","float32","float64","float96","float128","range","decimal"].includes(t)?ye:"categorical"===t?xe:t.startsWith("list")?Te:re):re}(e.arrowType)),i}const Ke=function(e,t,n){const o=i.useMemo((()=>function(e){if(!e)return new Map;try{return new Map(Object.entries(JSON.parse(e)))}catch(t){return(0,oe.H)(t),new Map}}(e.columns)),[e.columns]),a=e.useContainerWidth||(0,h.bb)(e.width)&&e.width>0;return{columns:i.useMemo((()=>{let i=function(e){const t=[],{dimensions:n}=e,i=n.headerColumns,o=n.dataColumns;if(0===i&&0===o)return t.push({id:"empty-index",title:"",indexNumber:0,isEditable:!1,isIndex:!0}),t;for(let a=0;a<i;a++){const n={...ke(e,a),indexNumber:a};t.push(n)}for(let a=0;a<o;a++){const n={...Me(e,a),indexNumber:a+i};t.push(n)}return t}(t).map((t=>{let i={...t,...qe(t,o),isStretched:a};const l=Je(i);return(e.editingMode===m.Eh.EditingMode.READ_ONLY||n||!1===l.isEditableType)&&(i={...i,isEditable:!1}),e.editingMode!==m.Eh.EditingMode.READ_ONLY&&1==i.isEditable&&(i={...i,icon:"editable"},i.isRequired&&e.editingMode===m.Eh.EditingMode.DYNAMIC&&(i={...i,isHidden:!1})),l(i)})).filter((e=>!e.isHidden));if(e.columnOrder&&e.columnOrder.length>0){const t=[];i.forEach((e=>{e.isIndex&&t.push(e)})),e.columnOrder.forEach((e=>{const n=i.find((t=>t.name===e));n&&!n.isIndex&&t.push(n)})),i=t}return i.length>0?i:[re({id:"empty-index",title:"",indexNumber:0,isEditable:!1,isIndex:!0})]}),[t,o,a,n,e.editingMode,e.columnOrder])}};function Xe(e){return e.isIndex?Be:(0,h.le)(e.name)?"":e.name}const Ge=class{constructor(e){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[],this.numRows=0,this.numRows=e}toJson(e){const t=new Map;e.forEach((e=>{t.set(e.indexNumber,e)}));const n={edited_rows:{},added_rows:[],deleted_rows:[]};this.editedCells.forEach(((e,i,o)=>{const a={};e.forEach(((e,n,i)=>{const o=t.get(n);o&&(a[Xe(o)]=o.getCellValue(e))})),n.edited_rows[i]=a})),this.addedRows.forEach((e=>{const i={};let o=!1;e.forEach(((e,n,a)=>{const l=t.get(n);if(l){const t=l.getCellValue(e);l.isRequired&&l.isEditable&&P(e)&&(o=!0),(0,h.bb)(t)&&(i[Xe(l)]=t)}})),o||n.added_rows.push(i)})),n.deleted_rows=this.deletedRows;return JSON.stringify(n,((e,t)=>void 0===t?null:t))}fromJson(e,t){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[];const n=JSON.parse(e),i=new Map;t.forEach((e=>{i.set(e.indexNumber,e)}));const o=new Map;t.forEach((e=>{o.set(Xe(e),e)})),Object.keys(n.edited_rows).forEach((e=>{const t=Number(e),i=n.edited_rows[e];Object.keys(i).forEach((e=>{const n=i[e],a=o.get(e);if(a){const e=a.getCell(n);var l;if(e)this.editedCells.has(t)||this.editedCells.set(t,new Map),null===(l=this.editedCells.get(t))||void 0===l||l.set(a.indexNumber,e)}}))})),n.added_rows.forEach((e=>{const t=new Map;Object.keys(e).forEach((n=>{const i=e[n],a=o.get(n);if(a){const e=a.getCell(i);e&&t.set(a.indexNumber,e)}})),this.addedRows.push(t)})),this.deletedRows=n.deleted_rows}isAddedRow(e){return e>=this.numRows}getCell(e,t){if(this.isAddedRow(t))return this.addedRows[t-this.numRows].get(e);const n=this.editedCells.get(t);return void 0!==n?n.get(e):void 0}setCell(e,t,n){if(this.isAddedRow(t)){if(t-this.numRows>=this.addedRows.length)return;this.addedRows[t-this.numRows].set(e,n)}else{void 0===this.editedCells.get(t)&&this.editedCells.set(t,new Map);this.editedCells.get(t).set(e,n)}}addRow(e){this.addedRows.push(e)}deleteRows(e){e.sort(((e,t)=>t-e)).forEach((e=>{this.deleteRow(e)}))}deleteRow(e){(0,h.le)(e)||e<0||(this.isAddedRow(e)?this.addedRows.splice(e-this.numRows,1):(this.deletedRows.includes(e)||(this.deletedRows.push(e),this.deletedRows=this.deletedRows.sort(((e,t)=>e-t))),this.editedCells.delete(e)))}getOriginalRowIndex(e){let t=e;for(let n=0;n<this.deletedRows.length&&!(this.deletedRows[n]>t);n++)t+=1;return t}getNumRows(){return this.numRows+this.addedRows.length-this.deletedRows.length}};var Ue=n(35704);const Qe=function(){const e=(0,g.u)(),t=i.useMemo((()=>({editable:e=>'<svg xmlns="http://www.w3.org/2000/svg" height="40" viewBox="0 96 960 960" width="40" fill="'.concat(e.bgColor,'"><path d="m800.641 679.743-64.384-64.384 29-29q7.156-6.948 17.642-6.948 10.485 0 17.742 6.948l29 29q6.948 7.464 6.948 17.95 0 10.486-6.948 17.434l-29 29Zm-310.64 246.256v-64.383l210.82-210.821 64.384 64.384-210.821 210.82h-64.383Zm-360-204.872v-50.254h289.743v50.254H130.001Zm0-162.564v-50.255h454.615v50.255H130.001Zm0-162.307v-50.255h454.615v50.255H130.001Z"/></svg>')})),[]);return{accentColor:e.colors.primary,accentFg:e.colors.white,accentLight:(0,Ue.DZ)(e.colors.primary,.9),borderColor:e.colors.fadedText05,horizontalBorderColor:e.colors.fadedText05,fontFamily:e.genericFonts.bodyFont,bgSearchResult:(0,Ue.DZ)(e.colors.primary,.9),bgIconHeader:e.colors.fadedText60,fgIconHeader:e.colors.white,bgHeader:e.colors.bgMix,bgHeaderHasFocus:e.colors.secondaryBg,bgHeaderHovered:e.colors.secondaryBg,textHeader:e.colors.fadedText60,textHeaderSelected:e.colors.white,textGroupHeader:e.colors.fadedText60,headerFontStyle:"".concat(e.fontSizes.sm),baseFontStyle:e.fontSizes.sm,editorFontSize:e.fontSizes.sm,textDark:e.colors.bodyText,textMedium:(0,Ue.DZ)(e.colors.bodyText,.2),textLight:e.colors.fadedText40,textBubble:e.colors.fadedText60,bgCell:e.colors.bgColor,bgCellMedium:e.colors.bgColor,cellHorizontalPadding:8,cellVerticalPadding:3,bgBubble:e.colors.secondaryBg,bgBubbleSelected:e.colors.secondaryBg,linkColor:e.colors.linkText,drilldownBorder:e.colors.darkenedBgMix25,tableBorderRadius:e.radii.lg,headerIcons:t}};const $e=function(e,t,n,o){return{getCellContent:i.useCallback((i=>{let[a,l]=i;if(a>t.length-1)return W("Column index out of bounds.","This should never happen. Please report this bug.");if(l>n-1)return W("Row index out of bounds.","This should never happen. Please report this bug.");const r=t[a],s=r.indexNumber,d=o.current.getOriginalRowIndex(l);if(r.isEditable||o.current.isAddedRow(d)){const e=o.current.getCell(s,d);if(void 0!==e)return e}try{return Ne(r,e.getCell(d+1,s),e.cssStyles)}catch(c){return(0,oe.H)(c),W("Error during cell creation.","This should never happen. Please report this bug. \nError: ".concat(c))}}),[t,n,e,o])}};var et=n(37753);const tt=function(e,t,n){const[o,a]=i.useState(),{getCellContent:l,getOriginalIndex:r}=(0,et.fF)({columns:t.map((e=>q(e))),getCellContent:n,rows:e,sort:o}),s=i.useMemo((()=>function(e,t){return void 0===t?e:e.map((e=>e.id===t.column.id?{...e,title:"asc"===t.direction?"\u2191 ".concat(e.title):"\u2193 ".concat(e.title)}:e))}(t,o)),[t,o]),d=i.useCallback((e=>{let t="asc";const n=s[e];if(o&&o.column.id===n.id){if("asc"!==o.direction)return void a(void 0);t="desc"}a({column:q(n),direction:t,mode:n.sortMode})}),[o,s]);return{columns:s,sortColumn:d,getOriginalIndex:r,getCellContent:l}};var nt=n(95345);const it=",",ot='"',at='"',lt="\n";function rt(e){return e.map((e=>function(e){if((0,h.le)(e))return"";const t=X(e);if(new RegExp("[".concat([it,ot,lt].join(""),"]")).test(t))return"".concat(ot).concat(t.replace(new RegExp(ot,"g"),at+ot)).concat(ot);return t}(e))).join(it)+lt}const st=function(e,t,n){return{exportToCsv:i.useCallback((async()=>{const i=(new Date).toISOString().slice(0,16).replace(":","-"),o="".concat(i,"_export.csv"),a=await(0,nt.Kr)({suggestedName:o,types:[{accept:{"text/csv":[".csv"]}}],excludeAcceptAllOption:!1}),l=new TextEncoder,r=await a.createWritable();await r.write(l.encode("\ufeff"));const s=t.map((e=>e.name));await r.write(l.encode(rt(s)));for(let d=0;d<n;d++){const n=[];t.forEach(((t,i,o)=>{n.push(t.getCellValue(e([i,d])))})),await r.write(l.encode(rt(n)))}await r.close()}),[t,n,e])}};const dt=function(e,t,n,o,a,l,r){const s=i.useCallback(((t,i)=>{let[l,s]=t;const d=e[l];if(!d.isEditable)return;const c=d.indexNumber,u=n.current.getOriginalRowIndex(a(s)),m=o([l,s]),h=d.getCellValue(m),g=d.getCellValue(i);if(!B(m)&&g===h)return;const p=d.getCell(g,!0);B(p)?(0,oe.KE)("Not applying the cell edit since it causes this error:\n ".concat(p.data)):(n.current.setCell(c,u,{...p,lastUpdated:performance.now()}),r())}),[e,n,a,o,r]),d=i.useCallback((()=>{if(t)return;const i=new Map;e.forEach((e=>{i.set(e.indexNumber,e.getCell(e.defaultValue))})),n.current.addRow(i)}),[e,n,t]),c=i.useCallback((()=>{t||(d(),r())}),[d,r,t]),u=i.useCallback((i=>{var o;if(i.rows.length>0){if(t)return!0;const e=i.rows.toArray().map((e=>n.current.getOriginalRowIndex(a(e))));return n.current.deleteRows(e),r(!0),!1}if(null!==(o=i.current)&&void 0!==o&&o.range){const t=[],n=i.current.range;for(let i=n.y;i<n.y+n.height;i++)for(let o=n.x;o<n.x+n.width;o++){const n=e[o];n.isEditable&&!n.isRequired&&(t.push({cell:[o,i]}),s([o,i],n.getCell(null)))}return t.length>0&&(r(),l(t)),!1}return!0}),[e,n,t,l,a,r,s]),m=i.useCallback(((i,s)=>{const[c,u]=i,m=[];for(let g=0;g<s.length;g++){const i=s[g];if(g+u>=n.current.getNumRows()){if(t)break;d()}for(let t=0;t<i.length;t++){const l=i[t],r=g+u,s=t+c;if(s>=e.length)break;const d=e[s];if(d.isEditable){const e=d.getCell(l,!0);if((0,h.bb)(e)&&!B(e)){const t=d.indexNumber,i=n.current.getOriginalRowIndex(a(r)),l=d.getCellValue(o([s,r]));d.getCellValue(e)!==l&&(n.current.setCell(t,i,{...e,lastUpdated:performance.now()}),m.push({cell:[s,r]}))}}}m.length>0&&(r(),l(m))}return!1}),[e,n,t,a,o,d,r,l]),g=i.useCallback(((t,n)=>{const i=t[0];if(i>=e.length)return!0;const o=e[i];if(o.validateInput){const e=o.validateInput(o.getCellValue(n));return!0===e||!1===e?e:o.getCell(e)}return!0}),[e]);return{onCellEdited:s,onPaste:m,onRowAppended:c,onDelete:u,validateCell:g}};const ct=function(e,t){const[n,o]=i.useState(),a=i.useRef(null),l=i.useCallback((n=>{if(clearTimeout(a.current),a.current=0,o(void 0),("header"===n.kind||"cell"===n.kind)&&n.location){const i=n.location[0],l=n.location[1];let r;if(i<0||i>=e.length)return;const s=e[i];if("header"===n.kind&&(0,h.bb)(s))r=s.help;else if("cell"===n.kind){const e=t([i,l]);s.isRequired&&s.isEditable&&P(e)?r="\u26a0\ufe0f Please fill out this cell.":function(e){return e.hasOwnProperty("tooltip")&&""!==e.tooltip}(e)&&(r=e.tooltip)}r&&(a.current=setTimeout((()=>{r&&o({content:r,left:n.bounds.x+n.bounds.width/2,top:n.bounds.y})}),600))}}),[e,t,o,a]);return{tooltip:n,clearTooltip:i.useCallback((()=>{o(void 0)}),[o]),onItemHovered:l}};var ut=n(94379);const mt=function(e,t){return{drawCell:i.useCallback((n=>{const{cell:i,theme:a,ctx:l,rect:r}=n,s=t?n.col-1:n.col;if(P(i)&&s<e.length){let t=!1;const i=e[s];return["checkbox","line_chart","bar_chart","progress"].includes(i.kind)||((e=>{const{cell:t,theme:n}=e;(0,o.uN)({...e,theme:{...n,textDark:n.textLight,textMedium:n.textLight},spriteManager:{},hyperWrapping:!1},"None",t.contentAlign)})(n),t=!0),i.isRequired&&i.isEditable&&function(e,t,n){e.save(),e.beginPath(),e.moveTo(t.x+t.width-8,t.y+1),e.lineTo(t.x+t.width,t.y+1),e.lineTo(t.x+t.width,t.y+1+8),e.fillStyle=n.accentColor,e.fill(),e.restore()}(l,r,a),t}return!1}),[e,t]),customRenderers:[...(0,ut.Bn)().customRenderers,...We]}};const ht=function(e){const[t,n]=(0,i.useState)((()=>new Map)),o=i.useCallback(((e,i,o,a)=>{e.id&&n(new Map(t).set(e.id,a))}),[t]);return{columns:i.useMemo((()=>e.map((e=>e.id&&t.has(e.id)&&void 0!==t.get(e.id)?{...e,width:t.get(e.id),grow:0}:e))),[e,t]),onColumnResize:o}},gt=35,pt=2*gt+3;const ft=function(e,t,n,o,a){let l,r=function(e){return Math.max(e*gt+1+2,pt)}(t+1+(e.editingMode===m.Eh.EditingMode.DYNAMIC?1:0)),s=Math.min(r,400);e.height&&(s=Math.max(e.height,pt),r=Math.max(e.height,r)),o&&(s=Math.min(s,o),r=Math.min(r,o),e.height||(s=r));let d=n;e.useContainerWidth?l=n:e.width&&(l=Math.min(Math.max(e.width,52),n),d=Math.min(Math.max(e.width,d),n));const[c,u]=i.useState({width:l||"100%",height:s});return i.useLayoutEffect((()=>{e.useContainerWidth&&"100%"===c.width&&u({width:n,height:c.height})}),[n]),i.useLayoutEffect((()=>{u({width:c.width,height:s})}),[t]),i.useLayoutEffect((()=>{u({width:l||"100%",height:c.height})}),[l]),i.useLayoutEffect((()=>{u({width:c.width,height:s})}),[s]),i.useLayoutEffect((()=>{if(a){const t=e.useContainerWidth||(0,h.bb)(e.width)&&e.width>0;u({width:t?d:"100%",height:r})}else u({width:l||"100%",height:s})}),[a]),{minHeight:pt,maxHeight:r,minWidth:52,maxWidth:d,resizableSize:c,setResizableSize:u}},bt=(0,T.Z)("img",{target:"e24uaba0"})((()=>({maxWidth:"100%",maxHeight:"600px",objectFit:"scale-down"})),""),vt=e=>{let{urls:t}=e;const n=t&&t.length>0?t[0]:"";return n.startsWith("http")?(0,N.jsx)("a",{href:n,target:"_blank",rel:"noreferrer noopener",children:(0,N.jsx)(bt,{src:n})}):(0,N.jsx)(bt,{src:n})};var yt=n(31572),wt=n(13553),xt=n(80152);const Ct=function(e){let{top:t,left:n,content:o,clearTooltip:a}=e;const[l,r]=i.useState(!0),s=(0,g.u)(),{colors:d,fontSizes:c,radii:u}=s,m=i.useCallback((()=>{r(!1),a()}),[a,r]);return(0,N.jsx)(yt.Z,{content:(0,N.jsx)(xt.Uo,{className:"stTooltipContent",children:(0,N.jsx)(b.ZP,{style:{fontSize:c.sm},source:o,allowHTML:!1})}),placement:wt.r4.top,accessibilityType:wt.SI.tooltip,showArrow:!1,popoverMargin:5,onClickOutside:m,onEsc:m,overrides:{Body:{style:{borderTopLeftRadius:u.md,borderTopRightRadius:u.md,borderBottomLeftRadius:u.md,borderBottomRightRadius:u.md,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{backgroundColor:(0,E.Iy)(s)?d.bgColor:d.secondaryBg,color:d.bodyText,fontSize:c.sm,fontWeight:"normal",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:l,children:(0,N.jsx)("div",{className:"stTooltipTarget","data-testid":"stTooltipTarget",style:{position:"fixed",top:t,left:n}})})},Tt=(0,T.Z)("div",{target:"e1w7nams0"})((e=>{let{hasCustomizedScrollbars:t,theme:n}=e;return{position:"relative",display:"inline-block","& .glideDataEditor":{height:"100%",minWidth:"100%",borderRadius:n.radii.lg},"& .dvn-scroller":{...!t&&{scrollbarWidth:"thin"},overflowX:"auto !important",overflowY:"auto !important"}}}),"");n(2739),n(24665);const Et=(0,u.Z)((function(e){let{element:t,data:n,width:u,height:g,disabled:p,widgetMgr:f,isFullScreen:b,expand:v,collapse:y}=e;const w=i.useRef(null),x=i.useRef(null),C=i.useRef(null),T=Qe(),[E,k]=i.useState(!0),[M,_]=i.useState(!1),[O,I]=i.useState(!1),[D,H]=i.useState(!1),A=i.useMemo((()=>window.matchMedia&&window.matchMedia("(pointer: coarse)").matches),[]),z=i.useMemo((()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")||window.navigator.userAgent.includes("Chrome")),[]),[V,F]=i.useState({columns:o.EV.empty(),rows:o.EV.empty(),current:void 0}),j=i.useCallback((()=>{F({columns:o.EV.empty(),rows:o.EV.empty(),current:void 0})}),[]),L=i.useCallback((()=>{F({columns:V.columns,rows:V.rows,current:void 0})}),[V]),W=i.useCallback((e=>{var t;null===(t=x.current)||void 0===t||t.updateCells(e)}),[]);(0,h.le)(t.editingMode)&&(t.editingMode=m.Eh.EditingMode.READ_ONLY);const{READ_ONLY:B,DYNAMIC:P}=m.Eh.EditingMode,Y=n.dimensions,J=Math.max(0,Y.rows-1),K=0===J&&!(t.editingMode===P&&Y.dataColumns>0),X=J>15e4,G=i.useRef(new Ge(J)),[U,Q]=i.useState(G.current.getNumRows());i.useEffect((()=>{G.current=new Ge(J),Q(G.current.getNumRows())}),[J]);const $=i.useCallback((()=>{G.current=new Ge(J),Q(G.current.getNumRows())}),[J]),{columns:ee}=Ke(t,n,p);i.useEffect((()=>{if(t.editingMode!==B){const e=f.getStringValue(t);e&&(G.current.fromJson(e,ee),Q(G.current.getNumRows()))}}),[]);const{getCellContent:te}=$e(n,ee,U,G),{columns:ne,sortColumn:ie,getOriginalIndex:oe,getCellContent:ae}=tt(J,ee,te),le=i.useCallback((function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];U!==G.current.getNumRows()&&Q(G.current.getNumRows()),e&&j(),(0,h.Ds)(100,(()=>{const e=G.current.toJson(ne);let i=f.getStringValue(t);void 0===i&&(i=new Ge(0).toJson([])),e!==i&&f.setStringValue(t,e,{fromUi:n})}))()}),[f,t,U,j,ne]),{exportToCsv:re}=st(ae,ne,U),{onCellEdited:se,onPaste:de,onRowAppended:ce,onDelete:ue,validateCell:me}=dt(ne,t.editingMode!==P,G,ae,oe,W,le),{tooltip:he,clearTooltip:ge,onItemHovered:pe}=ct(ne,ae),{drawCell:fe,customRenderers:be}=mt(ne,t.editingMode===P),{columns:ve,onColumnResize:ye}=ht(ne.map((e=>q(e)))),{minHeight:we,maxHeight:xe,minWidth:Ce,maxWidth:Te,resizableSize:Ee,setResizableSize:ke}=ft(t,U,u,g,b),Me=i.useCallback((e=>{let[t,n]=e;return{...Z(!0,!1),displayData:"empty",contentAlign:"center",allowOverlay:!1,themeOverride:{textDark:T.textLight},span:[0,Math.max(ne.length-1,0)]}}),[ne,T.textLight]);i.useEffect((()=>{const e=new c.K;return e.manageFormClearListener(f,t.formId,$),()=>{e.disconnect()}}),[t.formId,$,f]);const Ne=!K&&t.editingMode===P&&!p,Re=V.rows.length>0,Se=void 0!==V.current,_e=K?0:ne.filter((e=>e.isIndex)).length;return i.useEffect((()=>{if(C.current){var e,t;const n=null===(e=C.current)||void 0===e||null===(t=e.querySelector(".dvn-stack"))||void 0===t?void 0:t.getBoundingClientRect();n&&(I(n.height>C.current.clientHeight),H(n.width>C.current.clientWidth))}}),[Ee,U,ve]),(0,N.jsxs)(Tt,{"data-testid":"stDataFrame",className:"stDataFrame",hasCustomizedScrollbars:z,ref:C,onMouseDown:e=>{if(C.current&&z){const t=C.current.getBoundingClientRect();D&&t.height-7<e.clientY-t.top&&e.stopPropagation(),O&&t.width-7<e.clientX-t.left&&e.stopPropagation()}},onBlur:e=>{E||A||e.currentTarget.contains(e.relatedTarget)||L()},children:[(0,N.jsxs)(S,{isFullScreen:b,locked:Re||Se||A&&E,onExpand:v,onCollapse:y,target:Tt,children:[Ne&&Re&&(0,N.jsx)(R,{label:"Delete row(s)",icon:l.H,onClick:()=>{ue&&(ue(V),ge())}}),Ne&&!Re&&(0,N.jsx)(R,{label:"Add row",icon:r.m,onClick:()=>{ce&&(k(!0),ce(),ge())}}),!X&&!K&&(0,N.jsx)(R,{label:"Download as CSV",icon:s.k,onClick:()=>re()}),!K&&(0,N.jsx)(R,{label:"Search",icon:d.o,onClick:()=>{M?_(!1):(k(!0),_(!0)),ge()}})]}),(0,N.jsx)(a.e,{"data-testid":"stDataFrameResizable",ref:w,defaultSize:Ee,style:{border:"1px solid ".concat(T.borderColor),borderRadius:"".concat(T.tableBorderRadius)},minHeight:we,maxHeight:xe,minWidth:Ce,maxWidth:Te,size:Ee,enable:{top:!1,right:!1,bottom:!1,left:!1,topRight:!1,bottomRight:!0,bottomLeft:!1,topLeft:!1},grid:[1,gt],snapGap:gt/3,onResizeStop:(e,t,n,i)=>{w.current&&ke({width:w.current.size.width,height:xe-w.current.size.height===3?w.current.size.height+3:w.current.size.height})},children:(0,N.jsx)(o.Nd,{className:"glideDataEditor",ref:x,columns:ve,rows:K?1:U,minColumnWidth:50,maxColumnWidth:1e3,maxColumnAutoWidth:500,rowHeight:gt,headerHeight:gt,getCellContent:K?Me:ae,onColumnResize:ye,freezeColumns:_e,smoothScrollX:!0,smoothScrollY:!0,verticalBorder:e=>!(e>=ne.length&&(t.useContainerWidth||"100%"===Ee.width)),getCellsForSelection:!0,rowMarkers:"none",rangeSelect:A?"none":"rect",columnSelect:"none",rowSelect:"none",onItemHovered:pe,keybindings:{downFill:!0},onKeyDown:e=>{(e.ctrlKey||e.metaKey)&&"f"===e.key&&(_((e=>!e)),e.stopPropagation(),e.preventDefault())},showSearch:M,onSearchClose:()=>{_(!1),ge()},onHeaderClicked:K||X?void 0:ie,gridSelection:V,onGridSelectionChange:e=>{(E||A)&&(F(e),void 0!==he&&ge())},theme:T,onMouseMove:e=>{"out-of-bounds"===e.kind&&E?k(!1):"out-of-bounds"===e.kind||E||k(!0)},fixedShadowX:!0,fixedShadowY:!0,experimental:{scrollbarWidthOverride:1,...z&&{paddingBottom:D?-6:void 0,paddingRight:O?-6:void 0}},drawCell:fe,customRenderers:be,imageEditorOverride:vt,headerIcons:T.headerIcons,validateCell:me,onPaste:!1,...!K&&t.editingMode!==B&&!p&&{fillHandle:!A,onCellEdited:se,onPaste:de,onDelete:ue},...!K&&t.editingMode===P&&{trailingRowOptions:{sticky:!1,tint:!0},rowMarkerTheme:{bgCell:T.bgHeader,bgCellMedium:T.bgHeader},rowMarkers:"checkbox",rowSelectionMode:"multi",rowSelect:p?"none":"multi",onRowAppended:p?void 0:ce,onHeaderClicked:void 0}})}),he&&he.content&&(0,N.jsx)(Ct,{top:he.top,left:he.left,content:he.content,clearTooltip:ge})]})}),!0)},87814:(e,t,n)=>{n.d(t,{K:()=>o});var i=n(50641);class o{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(e,t,n){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,i.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,n),this.lastWidgetMgr=e,this.lastFormId=t))}disconnect(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}}}]);