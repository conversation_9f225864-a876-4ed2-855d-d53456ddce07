"use strict";function _get(){return _get="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=_superPropBase(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},_get.apply(this,arguments)}function _superPropBase(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=_getPrototypeOf(e)););return e}function _inherits(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var n,r=_getPrototypeOf(e);if(t){var i=_getPrototypeOf(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return _possibleConstructorReturn(this,n)}}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _wrapNativeSuper(e){var t="function"===typeof Map?new Map:void 0;return _wrapNativeSuper=function(e){if(null===e||!_isNativeFunction(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return _construct(e,arguments,_getPrototypeOf(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(n,e)},_wrapNativeSuper(e)}function _construct(e,t,n){return _construct=_isNativeReflectConstruct()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&_setPrototypeOf(i,n.prototype),i},_construct.apply(null,arguments)}function _isNativeReflectConstruct(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function _isNativeFunction(e){return-1!==Function.toString.call(e).indexOf("[native code]")}function _setPrototypeOf(e,t){return _setPrototypeOf=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},_setPrototypeOf(e,t)}function _getPrototypeOf(e){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},_getPrototypeOf(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}Object.defineProperty(exports,"__esModule",{value:!0});var OrderedMap=require("orderedmap");function _interopDefaultLegacy(e){return e&&"object"===_typeof(e)&&"default"in e?e:{default:e}}var OrderedMap__default=_interopDefaultLegacy(OrderedMap);function _findDiffStart(e,t,n){for(var r=0;;r++){if(r==e.childCount||r==t.childCount)return e.childCount==t.childCount?null:n;var i=e.child(r),o=t.child(r);if(i!=o){if(!i.sameMarkup(o))return n;if(i.isText&&i.text!=o.text){for(var s=0;i.text[s]==o.text[s];s++)n++;return n}if(i.content.size||o.content.size){var a=_findDiffStart(i.content,o.content,n+1);if(null!=a)return a}n+=i.nodeSize}else n+=i.nodeSize}}function _findDiffEnd(e,t,n,r){for(var i=e.childCount,o=t.childCount;;){if(0==i||0==o)return i==o?null:{a:n,b:r};var s=e.child(--i),a=t.child(--o),h=s.nodeSize;if(s!=a){if(!s.sameMarkup(a))return{a:n,b:r};if(s.isText&&s.text!=a.text){for(var u=0,c=Math.min(s.text.length,a.text.length);u<c&&s.text[s.text.length-u-1]==a.text[a.text.length-u-1];)u++,n--,r--;return{a:n,b:r}}if(s.content.size||a.content.size){var l=_findDiffEnd(s.content,a.content,n-1,r-1);if(l)return l}n-=h,r-=h}else n-=h,r-=h}}var Fragment=function(){function e(t,n){if(_classCallCheck(this,e),this.content=t,this.size=n||0,null==n)for(var r=0;r<t.length;r++)this.size+=t[r].nodeSize}return _createClass(e,[{key:"nodesBetween",value:function(e,t,n){for(var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4?arguments[4]:void 0,o=0,s=0;s<t;o++){var a=this.content[o],h=s+a.nodeSize;if(h>e&&!1!==n(a,r+s,i||null,o)&&a.content.size){var u=s+1;a.nodesBetween(Math.max(0,e-u),Math.min(a.content.size,t-u),n,r+u)}s=h}}},{key:"descendants",value:function(e){this.nodesBetween(0,this.size,e)}},{key:"textBetween",value:function(e,t,n,r){var i="",o=!0;return this.nodesBetween(e,t,(function(s,a){s.isText?(i+=s.text.slice(Math.max(e,a)-a,t-a),o=!n):s.isLeaf?(r?i+="function"===typeof r?r(s):r:s.type.spec.leafText&&(i+=s.type.spec.leafText(s)),o=!n):!o&&s.isBlock&&(i+=n,o=!0)}),0),i}},{key:"append",value:function(t){if(!t.size)return this;if(!this.size)return t;var n=this.lastChild,r=t.firstChild,i=this.content.slice(),o=0;for(n.isText&&n.sameMarkup(r)&&(i[i.length-1]=n.withText(n.text+r.text),o=1);o<t.content.length;o++)i.push(t.content[o]);return new e(i,this.size+t.size)}},{key:"cut",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.size;if(0==t&&n==this.size)return this;var r=[],i=0;if(n>t)for(var o=0,s=0;s<n;o++){var a=this.content[o],h=s+a.nodeSize;h>t&&((s<t||h>n)&&(a=a.isText?a.cut(Math.max(0,t-s),Math.min(a.text.length,n-s)):a.cut(Math.max(0,t-s-1),Math.min(a.content.size,n-s-1))),r.push(a),i+=a.nodeSize),s=h}return new e(r,i)}},{key:"cutByIndex",value:function(t,n){return t==n?e.empty:0==t&&n==this.content.length?this:new e(this.content.slice(t,n))}},{key:"replaceChild",value:function(t,n){var r=this.content[t];if(r==n)return this;var i=this.content.slice(),o=this.size+n.nodeSize-r.nodeSize;return i[t]=n,new e(i,o)}},{key:"addToStart",value:function(t){return new e([t].concat(this.content),this.size+t.nodeSize)}},{key:"addToEnd",value:function(t){return new e(this.content.concat(t),this.size+t.nodeSize)}},{key:"eq",value:function(e){if(this.content.length!=e.content.length)return!1;for(var t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}},{key:"firstChild",get:function(){return this.content.length?this.content[0]:null}},{key:"lastChild",get:function(){return this.content.length?this.content[this.content.length-1]:null}},{key:"childCount",get:function(){return this.content.length}},{key:"child",value:function(e){var t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}},{key:"maybeChild",value:function(e){return this.content[e]||null}},{key:"forEach",value:function(e){for(var t=0,n=0;t<this.content.length;t++){var r=this.content[t];e(r,n,t),n+=r.nodeSize}}},{key:"findDiffStart",value:function(e){return _findDiffStart(this,e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)}},{key:"findDiffEnd",value:function(e){return _findDiffEnd(this,e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.size,arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.size)}},{key:"findIndex",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;if(0==e)return retIndex(0,e);if(e==this.size)return retIndex(this.content.length,e);if(e>this.size||e<0)throw new RangeError("Position ".concat(e," outside of fragment (").concat(this,")"));for(var n=0,r=0;;n++){var i=r+this.child(n).nodeSize;if(i>=e)return i==e||t>0?retIndex(n+1,i):retIndex(n,r);r=i}}},{key:"toString",value:function(){return"<"+this.toStringInner()+">"}},{key:"toStringInner",value:function(){return this.content.join(", ")}},{key:"toJSON",value:function(){return this.content.length?this.content.map((function(e){return e.toJSON()})):null}}],[{key:"fromJSON",value:function(t,n){if(!n)return e.empty;if(!Array.isArray(n))throw new RangeError("Invalid input for Fragment.fromJSON");return new e(n.map(t.nodeFromJSON))}},{key:"fromArray",value:function(t){if(!t.length)return e.empty;for(var n,r=0,i=0;i<t.length;i++){var o=t[i];r+=o.nodeSize,i&&o.isText&&t[i-1].sameMarkup(o)?(n||(n=t.slice(0,i)),n[n.length-1]=o.withText(n[n.length-1].text+o.text)):n&&n.push(o)}return new e(n||t,r)}},{key:"from",value:function(t){if(!t)return e.empty;if(t instanceof e)return t;if(Array.isArray(t))return this.fromArray(t);if(t.attrs)return new e([t],t.nodeSize);throw new RangeError("Can not convert "+t+" to a Fragment"+(t.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}]),e}();Fragment.empty=new Fragment([],0);var found={index:0,offset:0};function retIndex(e,t){return found.index=e,found.offset=t,found}function compareDeep(e,t){if(e===t)return!0;if(!e||"object"!=_typeof(e)||!t||"object"!=_typeof(t))return!1;var n=Array.isArray(e);if(Array.isArray(t)!=n)return!1;if(n){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(!compareDeep(e[r],t[r]))return!1}else{for(var i in e)if(!(i in t)||!compareDeep(e[i],t[i]))return!1;for(var o in t)if(!(o in e))return!1}return!0}var Mark=function(){function e(t,n){_classCallCheck(this,e),this.type=t,this.attrs=n}return _createClass(e,[{key:"addToSet",value:function(e){for(var t,n=!1,r=0;r<e.length;r++){var i=e[r];if(this.eq(i))return e;if(this.type.excludes(i.type))t||(t=e.slice(0,r));else{if(i.type.excludes(this.type))return e;!n&&i.type.rank>this.type.rank&&(t||(t=e.slice(0,r)),t.push(this),n=!0),t&&t.push(i)}}return t||(t=e.slice()),n||t.push(this),t}},{key:"removeFromSet",value:function(e){for(var t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}},{key:"isInSet",value:function(e){for(var t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}},{key:"eq",value:function(e){return this==e||this.type==e.type&&compareDeep(this.attrs,e.attrs)}},{key:"toJSON",value:function(){var e={type:this.type.name};for(var t in this.attrs){e.attrs=this.attrs;break}return e}}],[{key:"fromJSON",value:function(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");var n=e.marks[t.type];if(!n)throw new RangeError("There is no mark type ".concat(t.type," in this schema"));return n.create(t.attrs)}},{key:"sameSet",value:function(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(var n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}},{key:"setFrom",value:function(t){if(!t||Array.isArray(t)&&0==t.length)return e.none;if(t instanceof e)return[t];var n=t.slice();return n.sort((function(e,t){return e.type.rank-t.type.rank})),n}}]),e}();Mark.none=[];var ReplaceError=function(e){_inherits(n,_wrapNativeSuper(Error));var t=_createSuper(n);function n(){return _classCallCheck(this,n),t.apply(this,arguments)}return _createClass(n)}(),Slice=function(){function e(t,n,r){_classCallCheck(this,e),this.content=t,this.openStart=n,this.openEnd=r}return _createClass(e,[{key:"size",get:function(){return this.content.size-this.openStart-this.openEnd}},{key:"insertAt",value:function(t,n){var r=insertInto(this.content,t+this.openStart,n);return r&&new e(r,this.openStart,this.openEnd)}},{key:"removeBetween",value:function(t,n){return new e(removeRange(this.content,t+this.openStart,n+this.openStart),this.openStart,this.openEnd)}},{key:"eq",value:function(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}},{key:"toString",value:function(){return this.content+"("+this.openStart+","+this.openEnd+")"}},{key:"toJSON",value:function(){if(!this.content.size)return null;var e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}}],[{key:"fromJSON",value:function(t,n){if(!n)return e.empty;var r=n.openStart||0,i=n.openEnd||0;if("number"!=typeof r||"number"!=typeof i)throw new RangeError("Invalid input for Slice.fromJSON");return new e(Fragment.fromJSON(t,n.content),r,i)}},{key:"maxOpen",value:function(t){for(var n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=0,i=0,o=t.firstChild;o&&!o.isLeaf&&(n||!o.type.spec.isolating);o=o.firstChild)r++;for(var s=t.lastChild;s&&!s.isLeaf&&(n||!s.type.spec.isolating);s=s.lastChild)i++;return new e(t,r,i)}}]),e}();function removeRange(e,t,n){var r=e.findIndex(t),i=r.index,o=r.offset,s=e.maybeChild(i),a=e.findIndex(n),h=a.index,u=a.offset;if(o==t||s.isText){if(u!=n&&!e.child(h).isText)throw new RangeError("Removing non-flat range");return e.cut(0,t).append(e.cut(n))}if(i!=h)throw new RangeError("Removing non-flat range");return e.replaceChild(i,s.copy(removeRange(s.content,t-o-1,n-o-1)))}function insertInto(e,t,n,r){var i=e.findIndex(t),o=i.index,s=i.offset,a=e.maybeChild(o);if(s==t||a.isText)return r&&!r.canReplace(o,o,n)?null:e.cut(0,t).append(n).append(e.cut(t));var h=insertInto(a.content,t-s-1,n);return h&&e.replaceChild(o,a.copy(h))}function _replace(e,t,n){if(n.openStart>e.depth)throw new ReplaceError("Inserted content deeper than insertion position");if(e.depth-n.openStart!=t.depth-n.openEnd)throw new ReplaceError("Inconsistent open depths");return replaceOuter(e,t,n,0)}function replaceOuter(e,t,n,r){var i=e.index(r),o=e.node(r);if(i==t.index(r)&&r<e.depth-n.openStart){var s=replaceOuter(e,t,n,r+1);return o.copy(o.content.replaceChild(i,s))}if(n.content.size){if(n.openStart||n.openEnd||e.depth!=r||t.depth!=r){var a=prepareSliceForReplace(n,e);return close(o,replaceThreeWay(e,a.start,a.end,t,r))}var h=e.parent,u=h.content;return close(h,u.cut(0,e.parentOffset).append(n.content).append(u.cut(t.parentOffset)))}return close(o,replaceTwoWay(e,t,r))}function checkJoin(e,t){if(!t.type.compatibleContent(e.type))throw new ReplaceError("Cannot join "+t.type.name+" onto "+e.type.name)}function joinable(e,t,n){var r=e.node(n);return checkJoin(r,t.node(n)),r}function addNode(e,t){var n=t.length-1;n>=0&&e.isText&&e.sameMarkup(t[n])?t[n]=e.withText(t[n].text+e.text):t.push(e)}function addRange(e,t,n,r){var i=(t||e).node(n),o=0,s=t?t.index(n):i.childCount;e&&(o=e.index(n),e.depth>n?o++:e.textOffset&&(addNode(e.nodeAfter,r),o++));for(var a=o;a<s;a++)addNode(i.child(a),r);t&&t.depth==n&&t.textOffset&&addNode(t.nodeBefore,r)}function close(e,t){if(!e.type.validContent(t))throw new ReplaceError("Invalid content for node "+e.type.name);return e.copy(t)}function replaceThreeWay(e,t,n,r,i){var o=e.depth>i&&joinable(e,t,i+1),s=r.depth>i&&joinable(n,r,i+1),a=[];return addRange(null,e,i,a),o&&s&&t.index(i)==n.index(i)?(checkJoin(o,s),addNode(close(o,replaceThreeWay(e,t,n,r,i+1)),a)):(o&&addNode(close(o,replaceTwoWay(e,t,i+1)),a),addRange(t,n,i,a),s&&addNode(close(s,replaceTwoWay(n,r,i+1)),a)),addRange(r,null,i,a),new Fragment(a)}function replaceTwoWay(e,t,n){var r=[];(addRange(null,e,n,r),e.depth>n)&&addNode(close(joinable(e,t,n+1),replaceTwoWay(e,t,n+1)),r);return addRange(t,null,n,r),new Fragment(r)}function prepareSliceForReplace(e,t){for(var n=t.depth-e.openStart,r=t.node(n).copy(e.content),i=n-1;i>=0;i--)r=t.node(i).copy(Fragment.from(r));return{start:r.resolveNoCache(e.openStart+n),end:r.resolveNoCache(r.content.size-e.openEnd-n)}}Slice.empty=new Slice(Fragment.empty,0,0);var ResolvedPos=function(){function e(t,n,r){_classCallCheck(this,e),this.pos=t,this.path=n,this.parentOffset=r,this.depth=n.length/3-1}return _createClass(e,[{key:"resolveDepth",value:function(e){return null==e?this.depth:e<0?this.depth+e:e}},{key:"parent",get:function(){return this.node(this.depth)}},{key:"doc",get:function(){return this.node(0)}},{key:"node",value:function(e){return this.path[3*this.resolveDepth(e)]}},{key:"index",value:function(e){return this.path[3*this.resolveDepth(e)+1]}},{key:"indexAfter",value:function(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}},{key:"start",value:function(e){return 0==(e=this.resolveDepth(e))?0:this.path[3*e-1]+1}},{key:"end",value:function(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}},{key:"before",value:function(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}},{key:"after",value:function(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}},{key:"textOffset",get:function(){return this.pos-this.path[this.path.length-1]}},{key:"nodeAfter",get:function(){var e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;var n=this.pos-this.path[this.path.length-1],r=e.child(t);return n?e.child(t).cut(n):r}},{key:"nodeBefore",get:function(){var e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):0==e?null:this.parent.child(e-1)}},{key:"posAtIndex",value:function(e,t){t=this.resolveDepth(t);for(var n=this.path[3*t],r=0==t?0:this.path[3*t-1]+1,i=0;i<e;i++)r+=n.child(i).nodeSize;return r}},{key:"marks",value:function(){var e=this.parent,t=this.index();if(0==e.content.size)return Mark.none;if(this.textOffset)return e.child(t).marks;var n=e.maybeChild(t-1),r=e.maybeChild(t);if(!n){var i=n;n=r,r=i}for(var o=n.marks,s=0;s<o.length;s++)!1!==o[s].type.spec.inclusive||r&&o[s].isInSet(r.marks)||(o=o[s--].removeFromSet(o));return o}},{key:"marksAcross",value:function(e){var t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;for(var n=t.marks,r=e.parent.maybeChild(e.index()),i=0;i<n.length;i++)!1!==n[i].type.spec.inclusive||r&&n[i].isInSet(r.marks)||(n=n[i--].removeFromSet(n));return n}},{key:"sharedDepth",value:function(e){for(var t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}},{key:"blockRange",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this,t=arguments.length>1?arguments[1]:void 0;if(e.pos<this.pos)return e.blockRange(this);for(var n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new NodeRange(this,e,n);return null}},{key:"sameParent",value:function(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}},{key:"max",value:function(e){return e.pos>this.pos?e:this}},{key:"min",value:function(e){return e.pos<this.pos?e:this}},{key:"toString",value:function(){for(var e="",t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}}],[{key:"resolve",value:function(t,n){if(!(n>=0&&n<=t.content.size))throw new RangeError("Position "+n+" out of range");for(var r=[],i=0,o=n,s=t;;){var a=s.content.findIndex(o),h=a.index,u=a.offset,c=o-u;if(r.push(s,h,i+u),!c)break;if((s=s.child(h)).isText)break;o=c-1,i+=u+1}return new e(n,r,o)}},{key:"resolveCached",value:function(t,n){for(var r=0;r<resolveCache.length;r++){var i=resolveCache[r];if(i.pos==n&&i.doc==t)return i}var o=resolveCache[resolveCachePos]=e.resolve(t,n);return resolveCachePos=(resolveCachePos+1)%resolveCacheSize,o}}]),e}(),resolveCache=[],resolveCachePos=0,resolveCacheSize=12,NodeRange=function(){function e(t,n,r){_classCallCheck(this,e),this.$from=t,this.$to=n,this.depth=r}return _createClass(e,[{key:"start",get:function(){return this.$from.before(this.depth+1)}},{key:"end",get:function(){return this.$to.after(this.depth+1)}},{key:"parent",get:function(){return this.$from.node(this.depth)}},{key:"startIndex",get:function(){return this.$from.index(this.depth)}},{key:"endIndex",get:function(){return this.$to.indexAfter(this.depth)}}]),e}(),emptyAttrs=Object.create(null),Node=function(){function e(t,n,r){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Mark.none;_classCallCheck(this,e),this.type=t,this.attrs=n,this.marks=i,this.content=r||Fragment.empty}return _createClass(e,[{key:"nodeSize",get:function(){return this.isLeaf?1:2+this.content.size}},{key:"childCount",get:function(){return this.content.childCount}},{key:"child",value:function(e){return this.content.child(e)}},{key:"maybeChild",value:function(e){return this.content.maybeChild(e)}},{key:"forEach",value:function(e){this.content.forEach(e)}},{key:"nodesBetween",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;this.content.nodesBetween(e,t,n,r,this)}},{key:"descendants",value:function(e){this.nodesBetween(0,this.content.size,e)}},{key:"textContent",get:function(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}},{key:"textBetween",value:function(e,t,n,r){return this.content.textBetween(e,t,n,r)}},{key:"firstChild",get:function(){return this.content.firstChild}},{key:"lastChild",get:function(){return this.content.lastChild}},{key:"eq",value:function(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}},{key:"sameMarkup",value:function(e){return this.hasMarkup(e.type,e.attrs,e.marks)}},{key:"hasMarkup",value:function(e,t,n){return this.type==e&&compareDeep(this.attrs,t||e.defaultAttrs||emptyAttrs)&&Mark.sameSet(this.marks,n||Mark.none)}},{key:"copy",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return t==this.content?this:new e(this.type,this.attrs,t,this.marks)}},{key:"mark",value:function(t){return t==this.marks?this:new e(this.type,this.attrs,this.content,t)}},{key:"cut",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.content.size;return 0==e&&t==this.content.size?this:this.copy(this.content.cut(e,t))}},{key:"slice",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.content.size,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(e==t)return Slice.empty;var r=this.resolve(e),i=this.resolve(t),o=n?0:r.sharedDepth(t),s=r.start(o),a=r.node(o).content.cut(r.pos-s,i.pos-s);return new Slice(a,r.depth-o,i.depth-o)}},{key:"replace",value:function(e,t,n){return _replace(this.resolve(e),this.resolve(t),n)}},{key:"nodeAt",value:function(e){for(var t=this;;){var n=t.content.findIndex(e),r=n.index,i=n.offset;if(!(t=t.maybeChild(r)))return null;if(i==e||t.isText)return t;e-=i+1}}},{key:"childAfter",value:function(e){var t=this.content.findIndex(e),n=t.index,r=t.offset;return{node:this.content.maybeChild(n),index:n,offset:r}}},{key:"childBefore",value:function(e){if(0==e)return{node:null,index:0,offset:0};var t=this.content.findIndex(e),n=t.index,r=t.offset;if(r<e)return{node:this.content.child(n),index:n,offset:r};var i=this.content.child(n-1);return{node:i,index:n-1,offset:r-i.nodeSize}}},{key:"resolve",value:function(e){return ResolvedPos.resolveCached(this,e)}},{key:"resolveNoCache",value:function(e){return ResolvedPos.resolve(this,e)}},{key:"rangeHasMark",value:function(e,t,n){var r=!1;return t>e&&this.nodesBetween(e,t,(function(e){return n.isInSet(e.marks)&&(r=!0),!r})),r}},{key:"isBlock",get:function(){return this.type.isBlock}},{key:"isTextblock",get:function(){return this.type.isTextblock}},{key:"inlineContent",get:function(){return this.type.inlineContent}},{key:"isInline",get:function(){return this.type.isInline}},{key:"isText",get:function(){return this.type.isText}},{key:"isLeaf",get:function(){return this.type.isLeaf}},{key:"isAtom",get:function(){return this.type.isAtom}},{key:"toString",value:function(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);var e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),wrapMarks(this.marks,e)}},{key:"contentMatchAt",value:function(e){var t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}},{key:"canReplace",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Fragment.empty,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:n.childCount,o=this.contentMatchAt(e).matchFragment(n,r,i),s=o&&o.matchFragment(this.content,t);if(!s||!s.validEnd)return!1;for(var a=r;a<i;a++)if(!this.type.allowsMarks(n.child(a).marks))return!1;return!0}},{key:"canReplaceWith",value:function(e,t,n,r){if(r&&!this.type.allowsMarks(r))return!1;var i=this.contentMatchAt(e).matchType(n),o=i&&i.matchFragment(this.content,t);return!!o&&o.validEnd}},{key:"canAppend",value:function(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}},{key:"check",value:function(){if(!this.type.validContent(this.content))throw new RangeError("Invalid content for node ".concat(this.type.name,": ").concat(this.content.toString().slice(0,50)));for(var e=Mark.none,t=0;t<this.marks.length;t++)e=this.marks[t].addToSet(e);if(!Mark.sameSet(e,this.marks))throw new RangeError("Invalid collection of marks for node ".concat(this.type.name,": ").concat(this.marks.map((function(e){return e.type.name}))));this.content.forEach((function(e){return e.check()}))}},{key:"toJSON",value:function(){var e={type:this.type.name};for(var t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map((function(e){return e.toJSON()}))),e}}],[{key:"fromJSON",value:function(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");var n=null;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if("text"==t.type){if("string"!=typeof t.text)throw new RangeError("Invalid text node in JSON");return e.text(t.text,n)}var r=Fragment.fromJSON(e,t.content);return e.nodeType(t.type).create(t.attrs,r,n)}}]),e}();Node.prototype.text=void 0;var TextNode=function(e){_inherits(n,Node);var t=_createSuper(n);function n(e,r,i,o){var s;if(_classCallCheck(this,n),s=t.call(this,e,r,null,o),!i)throw new RangeError("Empty text nodes are not allowed");return s.text=i,s}return _createClass(n,[{key:"toString",value:function(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):wrapMarks(this.marks,JSON.stringify(this.text))}},{key:"textContent",get:function(){return this.text}},{key:"textBetween",value:function(e,t){return this.text.slice(e,t)}},{key:"nodeSize",get:function(){return this.text.length}},{key:"mark",value:function(e){return e==this.marks?this:new n(this.type,this.attrs,this.text,e)}},{key:"withText",value:function(e){return e==this.text?this:new n(this.type,this.attrs,e,this.marks)}},{key:"cut",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.text.length;return 0==e&&t==this.text.length?this:this.withText(this.text.slice(e,t))}},{key:"eq",value:function(e){return this.sameMarkup(e)&&this.text==e.text}},{key:"toJSON",value:function(){var e=_get(_getPrototypeOf(n.prototype),"toJSON",this).call(this);return e.text=this.text,e}}]),n}();function wrapMarks(e,t){for(var n=e.length-1;n>=0;n--)t=e[n].type.name+"("+t+")";return t}var ContentMatch=function(){function e(t){_classCallCheck(this,e),this.validEnd=t,this.next=[],this.wrapCache=[]}return _createClass(e,[{key:"matchType",value:function(e){for(var t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}},{key:"matchFragment",value:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.childCount,r=this,i=t;r&&i<n;i++)r=r.matchType(e.child(i).type);return r}},{key:"inlineContent",get:function(){return this.next.length&&this.next[0].type.isInline}},{key:"defaultType",get:function(){for(var e=0;e<this.next.length;e++){var t=this.next[e].type;if(!t.isText&&!t.hasRequiredAttrs())return t}return null}},{key:"compatible",value:function(e){for(var t=0;t<this.next.length;t++)for(var n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}},{key:"fillBefore",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=[this];return function i(o,s){var a=o.matchFragment(e,n);if(a&&(!t||a.validEnd))return Fragment.from(s.map((function(e){return e.createAndFill()})));for(var h=0;h<o.next.length;h++){var u=o.next[h],c=u.type,l=u.next;if(!c.isText&&!c.hasRequiredAttrs()&&-1==r.indexOf(l)){r.push(l);var f=i(l,s.concat(c));if(f)return f}}return null}(this,[])}},{key:"findWrapping",value:function(e){for(var t=0;t<this.wrapCache.length;t+=2)if(this.wrapCache[t]==e)return this.wrapCache[t+1];var n=this.computeWrapping(e);return this.wrapCache.push(e,n),n}},{key:"computeWrapping",value:function(e){for(var t=Object.create(null),n=[{match:this,type:null,via:null}];n.length;){var r=n.shift(),i=r.match;if(i.matchType(e)){for(var o=[],s=r;s.type;s=s.via)o.push(s.type);return o.reverse()}for(var a=0;a<i.next.length;a++){var h=i.next[a],u=h.type,c=h.next;u.isLeaf||u.hasRequiredAttrs()||u.name in t||r.type&&!c.validEnd||(n.push({match:u.contentMatch,type:u,via:r}),t[u.name]=!0)}}return null}},{key:"edgeCount",get:function(){return this.next.length}},{key:"edge",value:function(e){if(e>=this.next.length)throw new RangeError("There's no ".concat(e,"th edge in this content match"));return this.next[e]}},{key:"toString",value:function(){var e=[];return function t(n){e.push(n);for(var r=0;r<n.next.length;r++)-1==e.indexOf(n.next[r].next)&&t(n.next[r].next)}(this),e.map((function(t,n){for(var r=n+(t.validEnd?"*":" ")+" ",i=0;i<t.next.length;i++)r+=(i?", ":"")+t.next[i].type.name+"->"+e.indexOf(t.next[i].next);return r})).join("\n")}}],[{key:"parse",value:function(t,n){var r=new TokenStream(t,n);if(null==r.next)return e.empty;var i=parseExpr(r);r.next&&r.err("Unexpected trailing text");var o=dfa(nfa(i));return checkForDeadEnds(o,r),o}}]),e}();ContentMatch.empty=new ContentMatch(!0);var TokenStream=function(){function e(t,n){_classCallCheck(this,e),this.string=t,this.nodeTypes=n,this.inline=null,this.pos=0,this.tokens=t.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}return _createClass(e,[{key:"next",get:function(){return this.tokens[this.pos]}},{key:"eat",value:function(e){return this.next==e&&(this.pos++||!0)}},{key:"err",value:function(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}]),e}();function parseExpr(e){var t=[];do{t.push(parseExprSeq(e))}while(e.eat("|"));return 1==t.length?t[0]:{type:"choice",exprs:t}}function parseExprSeq(e){var t=[];do{t.push(parseExprSubscript(e))}while(e.next&&")"!=e.next&&"|"!=e.next);return 1==t.length?t[0]:{type:"seq",exprs:t}}function parseExprSubscript(e){for(var t=parseExprAtom(e);;)if(e.eat("+"))t={type:"plus",expr:t};else if(e.eat("*"))t={type:"star",expr:t};else if(e.eat("?"))t={type:"opt",expr:t};else{if(!e.eat("{"))break;t=parseExprRange(e,t)}return t}function parseNum(e){/\D/.test(e.next)&&e.err("Expected number, got '"+e.next+"'");var t=Number(e.next);return e.pos++,t}function parseExprRange(e,t){var n=parseNum(e),r=n;return e.eat(",")&&(r="}"!=e.next?parseNum(e):-1),e.eat("}")||e.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:t}}function resolveName(e,t){var n=e.nodeTypes,r=n[t];if(r)return[r];var i=[];for(var o in n){var s=n[o];s.groups.indexOf(t)>-1&&i.push(s)}return 0==i.length&&e.err("No node type or group '"+t+"' found"),i}function parseExprAtom(e){if(e.eat("(")){var t=parseExpr(e);return e.eat(")")||e.err("Missing closing paren"),t}if(!/\W/.test(e.next)){var n=resolveName(e,e.next).map((function(t){return null==e.inline?e.inline=t.isInline:e.inline!=t.isInline&&e.err("Mixing inline and block content"),{type:"name",value:t}}));return e.pos++,1==n.length?n[0]:{type:"choice",exprs:n}}e.err("Unexpected token '"+e.next+"'")}function nfa(e){var t=[[]];return i(function e(t,o){if("choice"==t.type)return t.exprs.reduce((function(t,n){return t.concat(e(n,o))}),[]);if("seq"!=t.type){if("star"==t.type){var s=n();return r(o,s),i(e(t.expr,s),s),[r(s)]}if("plus"==t.type){var a=n();return i(e(t.expr,o),a),i(e(t.expr,a),a),[r(a)]}if("opt"==t.type)return[r(o)].concat(e(t.expr,o));if("range"==t.type){for(var h=o,u=0;u<t.min;u++){var c=n();i(e(t.expr,h),c),h=c}if(-1==t.max)i(e(t.expr,h),h);else for(var l=t.min;l<t.max;l++){var f=n();r(h,f),i(e(t.expr,h),f),h=f}return[r(h)]}if("name"==t.type)return[r(o,void 0,t.value)];throw new Error("Unknown expr type")}for(var p=0;;p++){var d=e(t.exprs[p],o);if(p==t.exprs.length-1)return d;i(d,o=n())}}(e,0),n()),t;function n(){return t.push([])-1}function r(e,n,r){var i={term:r,to:n};return t[e].push(i),i}function i(e,t){e.forEach((function(e){return e.to=t}))}}function cmp(e,t){return t-e}function nullFrom(e,t){var n=[];return function t(r){var i=e[r];if(1==i.length&&!i[0].term)return t(i[0].to);n.push(r);for(var o=0;o<i.length;o++){var s=i[o],a=s.term,h=s.to;a||-1!=n.indexOf(h)||t(h)}}(t),n.sort(cmp)}function dfa(e){var t=Object.create(null);return function n(r){var i=[];r.forEach((function(t){e[t].forEach((function(t){var n=t.term,r=t.to;if(n){for(var o,s=0;s<i.length;s++)i[s][0]==n&&(o=i[s][1]);nullFrom(e,r).forEach((function(e){o||i.push([n,o=[]]),-1==o.indexOf(e)&&o.push(e)}))}}))}));for(var o=t[r.join(",")]=new ContentMatch(r.indexOf(e.length-1)>-1),s=0;s<i.length;s++){var a=i[s][1].sort(cmp);o.next.push({type:i[s][0],next:t[a.join(",")]||n(a)})}return o}(nullFrom(e,0))}function checkForDeadEnds(e,t){for(var n=0,r=[e];n<r.length;n++){for(var i=r[n],o=!i.validEnd,s=[],a=0;a<i.next.length;a++){var h=i.next[a],u=h.type,c=h.next;s.push(u.name),!o||u.isText||u.hasRequiredAttrs()||(o=!1),-1==r.indexOf(c)&&r.push(c)}o&&t.err("Only non-generatable nodes ("+s.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}function defaultAttrs(e){var t=Object.create(null);for(var n in e){var r=e[n];if(!r.hasDefault)return null;t[n]=r.default}return t}function _computeAttrs(e,t){var n=Object.create(null);for(var r in e){var i=t&&t[r];if(void 0===i){var o=e[r];if(!o.hasDefault)throw new RangeError("No value supplied for attribute "+r);i=o.default}n[r]=i}return n}function initAttrs(e){var t=Object.create(null);if(e)for(var n in e)t[n]=new Attribute(e[n]);return t}var NodeType=function(){function e(t,n,r){_classCallCheck(this,e),this.name=t,this.schema=n,this.spec=r,this.markSet=null,this.groups=r.group?r.group.split(" "):[],this.attrs=initAttrs(r.attrs),this.defaultAttrs=defaultAttrs(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(r.inline||"text"==t),this.isText="text"==t}return _createClass(e,[{key:"isInline",get:function(){return!this.isBlock}},{key:"isTextblock",get:function(){return this.isBlock&&this.inlineContent}},{key:"isLeaf",get:function(){return this.contentMatch==ContentMatch.empty}},{key:"isAtom",get:function(){return this.isLeaf||!!this.spec.atom}},{key:"whitespace",get:function(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}},{key:"hasRequiredAttrs",value:function(){for(var e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}},{key:"compatibleContent",value:function(e){return this==e||this.contentMatch.compatible(e.contentMatch)}},{key:"computeAttrs",value:function(e){return!e&&this.defaultAttrs?this.defaultAttrs:_computeAttrs(this.attrs,e)}},{key:"create",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new Node(this,this.computeAttrs(e),Fragment.from(t),Mark.setFrom(n))}},{key:"createChecked",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;if(t=Fragment.from(t),!this.validContent(t))throw new RangeError("Invalid content for node "+this.name);return new Node(this,this.computeAttrs(e),t,Mark.setFrom(n))}},{key:"createAndFill",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;if(e=this.computeAttrs(e),(t=Fragment.from(t)).size){var r=this.contentMatch.fillBefore(t);if(!r)return null;t=r.append(t)}var i=this.contentMatch.matchFragment(t),o=i&&i.fillBefore(Fragment.empty,!0);return o?new Node(this,e,t.append(o),Mark.setFrom(n)):null}},{key:"validContent",value:function(e){var t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(var n=0;n<e.childCount;n++)if(!this.allowsMarks(e.child(n).marks))return!1;return!0}},{key:"allowsMarkType",value:function(e){return null==this.markSet||this.markSet.indexOf(e)>-1}},{key:"allowsMarks",value:function(e){if(null==this.markSet)return!0;for(var t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}},{key:"allowedMarks",value:function(e){if(null==this.markSet)return e;for(var t,n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:Mark.none:e}}],[{key:"compile",value:function(t,n){var r=Object.create(null);t.forEach((function(t,i){return r[t]=new e(t,n,i)}));var i=n.spec.topNode||"doc";if(!r[i])throw new RangeError("Schema is missing its top node type ('"+i+"')");if(!r.text)throw new RangeError("Every schema needs a 'text' type");for(var o in r.text.attrs)throw new RangeError("The text node type should not have attributes");return r}}]),e}(),Attribute=function(){function e(t){_classCallCheck(this,e),this.hasDefault=Object.prototype.hasOwnProperty.call(t,"default"),this.default=t.default}return _createClass(e,[{key:"isRequired",get:function(){return!this.hasDefault}}]),e}(),MarkType=function(){function e(t,n,r,i){_classCallCheck(this,e),this.name=t,this.rank=n,this.schema=r,this.spec=i,this.attrs=initAttrs(i.attrs),this.excluded=null;var o=defaultAttrs(this.attrs);this.instance=o?new Mark(this,o):null}return _createClass(e,[{key:"create",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return!e&&this.instance?this.instance:new Mark(this,_computeAttrs(this.attrs,e))}},{key:"removeFromSet",value:function(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}},{key:"isInSet",value:function(e){for(var t=0;t<e.length;t++)if(e[t].type==this)return e[t]}},{key:"excludes",value:function(e){return this.excluded.indexOf(e)>-1}}],[{key:"compile",value:function(t,n){var r=Object.create(null),i=0;return t.forEach((function(t,o){return r[t]=new e(t,i++,n,o)})),r}}]),e}(),Schema=function(){function e(t){_classCallCheck(this,e),this.cached=Object.create(null),this.spec={nodes:OrderedMap__default.default.from(t.nodes),marks:OrderedMap__default.default.from(t.marks||{}),topNode:t.topNode},this.nodes=NodeType.compile(this.spec.nodes,this),this.marks=MarkType.compile(this.spec.marks,this);var n=Object.create(null);for(var r in this.nodes){if(r in this.marks)throw new RangeError(r+" can not be both a node and a mark");var i=this.nodes[r],o=i.spec.content||"",s=i.spec.marks;i.contentMatch=n[o]||(n[o]=ContentMatch.parse(o,this.nodes)),i.inlineContent=i.contentMatch.inlineContent,i.markSet="_"==s?null:s?gatherMarks(this,s.split(" ")):""!=s&&i.inlineContent?null:[]}for(var a in this.marks){var h=this.marks[a],u=h.spec.excludes;h.excluded=null==u?[h]:""==u?[]:gatherMarks(this,u.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}return _createClass(e,[{key:"node",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;if("string"==typeof e)e=this.nodeType(e);else{if(!(e instanceof NodeType))throw new RangeError("Invalid node type: "+e);if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}return e.createChecked(t,n,r)}},{key:"text",value:function(e,t){var n=this.nodes.text;return new TextNode(n,n.defaultAttrs,e,Mark.setFrom(t))}},{key:"mark",value:function(e,t){return"string"==typeof e&&(e=this.marks[e]),e.create(t)}},{key:"nodeFromJSON",value:function(e){return Node.fromJSON(this,e)}},{key:"markFromJSON",value:function(e){return Mark.fromJSON(this,e)}},{key:"nodeType",value:function(e){var t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}]),e}();function gatherMarks(e,t){for(var n=[],r=0;r<t.length;r++){var i=t[r],o=e.marks[i],s=o;if(o)n.push(o);else for(var a in e.marks){var h=e.marks[a];("_"==i||h.spec.group&&h.spec.group.split(" ").indexOf(i)>-1)&&n.push(s=h)}if(!s)throw new SyntaxError("Unknown mark type: '"+t[r]+"'")}return n}var DOMParser=function(){function e(t,n){var r=this;_classCallCheck(this,e),this.schema=t,this.rules=n,this.tags=[],this.styles=[],n.forEach((function(e){e.tag?r.tags.push(e):e.style&&r.styles.push(e)})),this.normalizeLists=!this.tags.some((function(e){if(!/^(ul|ol)\b/.test(e.tag)||!e.node)return!1;var n=t.nodes[e.node];return n.contentMatch.matchType(n)}))}return _createClass(e,[{key:"parse",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=new ParseContext(this,t,!1);return n.addAll(e,t.from,t.to),n.finish()}},{key:"parseSlice",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=new ParseContext(this,t,!0);return n.addAll(e,t.from,t.to),Slice.maxOpen(n.finish())}},{key:"matchTag",value:function(e,t,n){for(var r=n?this.tags.indexOf(n)+1:0;r<this.tags.length;r++){var i=this.tags[r];if(matches(e,i.tag)&&(void 0===i.namespace||e.namespaceURI==i.namespace)&&(!i.context||t.matchesContext(i.context))){if(i.getAttrs){var o=i.getAttrs(e);if(!1===o)continue;i.attrs=o||void 0}return i}}}},{key:"matchStyle",value:function(e,t,n,r){for(var i=r?this.styles.indexOf(r)+1:0;i<this.styles.length;i++){var o=this.styles[i],s=o.style;if(!(0!=s.indexOf(e)||o.context&&!n.matchesContext(o.context)||s.length>e.length&&(61!=s.charCodeAt(e.length)||s.slice(e.length+1)!=t))){if(o.getAttrs){var a=o.getAttrs(t);if(!1===a)continue;o.attrs=a||void 0}return o}}}}],[{key:"schemaRules",value:function(e){var t=[];function n(e){for(var n=null==e.priority?50:e.priority,r=0;r<t.length;r++){var i=t[r];if((null==i.priority?50:i.priority)<n)break}t.splice(r,0,e)}var r=function(t){var r=e.marks[t].spec.parseDOM;r&&r.forEach((function(e){n(e=copy(e)),e.mark=t}))};for(var i in e.marks)r(i);var o=function(t){var r=e.nodes[t].spec.parseDOM;r&&r.forEach((function(e){n(e=copy(e)),e.node=t}))};for(var s in e.nodes)o(s);return t}},{key:"fromSchema",value:function(t){return t.cached.domParser||(t.cached.domParser=new e(t,e.schemaRules(t)))}}]),e}(),blockTags={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},ignoreTags={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},listTags={ol:!0,ul:!0},OPT_PRESERVE_WS=1,OPT_PRESERVE_WS_FULL=2,OPT_OPEN_LEFT=4;function wsOptionsFor(e,t,n){return null!=t?(t?OPT_PRESERVE_WS:0)|("full"===t?OPT_PRESERVE_WS_FULL:0):e&&"pre"==e.whitespace?OPT_PRESERVE_WS|OPT_PRESERVE_WS_FULL:n&~OPT_OPEN_LEFT}var NodeContext=function(){function e(t,n,r,i,o,s,a){_classCallCheck(this,e),this.type=t,this.attrs=n,this.marks=r,this.pendingMarks=i,this.solid=o,this.options=a,this.content=[],this.activeMarks=Mark.none,this.stashMarks=[],this.match=s||(a&OPT_OPEN_LEFT?null:t.contentMatch)}return _createClass(e,[{key:"findWrapping",value:function(e){if(!this.match){if(!this.type)return[];var t=this.type.contentMatch.fillBefore(Fragment.from(e));if(!t){var n,r=this.type.contentMatch;return(n=r.findWrapping(e.type))?(this.match=r,n):null}this.match=this.type.contentMatch.matchFragment(t)}return this.match.findWrapping(e.type)}},{key:"finish",value:function(e){if(!(this.options&OPT_PRESERVE_WS)){var t,n=this.content[this.content.length-1];if(n&&n.isText&&(t=/[ \t\r\n\u000c]+$/.exec(n.text))){var r=n;n.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=r.withText(r.text.slice(0,r.text.length-t[0].length))}}var i=Fragment.from(this.content);return!e&&this.match&&(i=i.append(this.match.fillBefore(Fragment.empty,!0))),this.type?this.type.create(this.attrs,i,this.marks):i}},{key:"popFromStashMark",value:function(e){for(var t=this.stashMarks.length-1;t>=0;t--)if(e.eq(this.stashMarks[t]))return this.stashMarks.splice(t,1)[0]}},{key:"applyPending",value:function(e){for(var t=0,n=this.pendingMarks;t<n.length;t++){var r=n[t];(this.type?this.type.allowsMarkType(r.type):markMayApply(r.type,e))&&!r.isInSet(this.activeMarks)&&(this.activeMarks=r.addToSet(this.activeMarks),this.pendingMarks=r.removeFromSet(this.pendingMarks))}}},{key:"inlineContext",value:function(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!blockTags.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}]),e}(),ParseContext=function(){function e(t,n,r){_classCallCheck(this,e),this.parser=t,this.options=n,this.isOpen=r,this.open=0;var i,o=n.topNode,s=wsOptionsFor(null,n.preserveWhitespace,0)|(r?OPT_OPEN_LEFT:0);i=o?new NodeContext(o.type,o.attrs,Mark.none,Mark.none,!0,n.topMatch||o.type.contentMatch,s):new NodeContext(r?null:t.schema.topNodeType,null,Mark.none,Mark.none,!0,null,s),this.nodes=[i],this.find=n.findPositions,this.needsBlock=!1}return _createClass(e,[{key:"top",get:function(){return this.nodes[this.open]}},{key:"addDOM",value:function(e){if(3==e.nodeType)this.addTextNode(e);else if(1==e.nodeType){var t=e.getAttribute("style"),n=t?this.readStyles(parseStyles(t)):null,r=this.top;if(null!=n)for(var i=0;i<n.length;i++)this.addPendingMark(n[i]);if(this.addElement(e),null!=n)for(var o=0;o<n.length;o++)this.removePendingMark(n[o],r)}}},{key:"addTextNode",value:function(e){var t=e.nodeValue,n=this.top;if(n.options&OPT_PRESERVE_WS_FULL||n.inlineContext(e)||/[^ \t\r\n\u000c]/.test(t)){if(n.options&OPT_PRESERVE_WS)t=n.options&OPT_PRESERVE_WS_FULL?t.replace(/\r\n?/g,"\n"):t.replace(/\r?\n|\r/g," ");else if(t=t.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(t)&&this.open==this.nodes.length-1){var r=n.content[n.content.length-1],i=e.previousSibling;(!r||i&&"BR"==i.nodeName||r.isText&&/[ \t\r\n\u000c]$/.test(r.text))&&(t=t.slice(1))}t&&this.insertNode(this.parser.schema.text(t)),this.findInText(e)}else this.findInside(e)}},{key:"addElement",value:function(e,t){var n,r=e.nodeName.toLowerCase();listTags.hasOwnProperty(r)&&this.parser.normalizeLists&&normalizeList(e);var i=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(n=this.parser.matchTag(e,this,t));if(i?i.ignore:ignoreTags.hasOwnProperty(r))this.findInside(e),this.ignoreFallback(e);else if(!i||i.skip||i.closeParent){i&&i.closeParent?this.open=Math.max(0,this.open-1):i&&i.skip.nodeType&&(e=i.skip);var o,s=this.top,a=this.needsBlock;if(blockTags.hasOwnProperty(r))o=!0,s.type||(this.needsBlock=!0);else if(!e.firstChild)return void this.leafFallback(e);this.addAll(e),o&&this.sync(s),this.needsBlock=a}else this.addElementByRule(e,i,!1===i.consuming?n:void 0)}},{key:"leafFallback",value:function(e){"BR"==e.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode("\n"))}},{key:"ignoreFallback",value:function(e){"BR"!=e.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"))}},{key:"readStyles",value:function(e){var t=Mark.none;e:for(var n=0;n<e.length;n+=2)for(var r=void 0;;){var i=this.parser.matchStyle(e[n],e[n+1],this,r);if(!i)continue e;if(i.ignore)return null;if(t=this.parser.schema.marks[i.mark].create(i.attrs).addToSet(t),!1!==i.consuming)break;r=i}return t}},{key:"addElementByRule",value:function(e,t,n){var r,i,o,s=this;t.node?(i=this.parser.schema.nodes[t.node]).isLeaf?this.insertNode(i.create(t.attrs))||this.leafFallback(e):r=this.enter(i,t.attrs||null,t.preserveWhitespace):(o=this.parser.schema.marks[t.mark].create(t.attrs),this.addPendingMark(o));var a=this.top;if(i&&i.isLeaf)this.findInside(e);else if(n)this.addElement(e,n);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach((function(e){return s.insertNode(e)}));else{var h=e;"string"==typeof t.contentElement?h=e.querySelector(t.contentElement):"function"==typeof t.contentElement?h=t.contentElement(e):t.contentElement&&(h=t.contentElement),this.findAround(e,h,!0),this.addAll(h)}r&&this.sync(a)&&this.open--,o&&this.removePendingMark(o,a)}},{key:"addAll",value:function(e,t,n){for(var r=t||0,i=t?e.childNodes[t]:e.firstChild,o=null==n?null:e.childNodes[n];i!=o;i=i.nextSibling,++r)this.findAtPoint(e,r),this.addDOM(i);this.findAtPoint(e,r)}},{key:"findPlace",value:function(e){for(var t,n,r=this.open;r>=0;r--){var i=this.nodes[r],o=i.findWrapping(e);if(o&&(!t||t.length>o.length)&&(t=o,n=i,!o.length))break;if(i.solid)break}if(!t)return!1;this.sync(n);for(var s=0;s<t.length;s++)this.enterInner(t[s],null,!1);return!0}},{key:"insertNode",value:function(e){if(e.isInline&&this.needsBlock&&!this.top.type){var t=this.textblockFromContext();t&&this.enterInner(t)}if(this.findPlace(e)){this.closeExtra();var n=this.top;n.applyPending(e.type),n.match&&(n.match=n.match.matchType(e.type));for(var r=n.activeMarks,i=0;i<e.marks.length;i++)n.type&&!n.type.allowsMarkType(e.marks[i].type)||(r=e.marks[i].addToSet(r));return n.content.push(e.mark(r)),!0}return!1}},{key:"enter",value:function(e,t,n){var r=this.findPlace(e.create(t));return r&&this.enterInner(e,t,!0,n),r}},{key:"enterInner",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3?arguments[3]:void 0;this.closeExtra();var i=this.top;i.applyPending(e),i.match=i.match&&i.match.matchType(e);var o=wsOptionsFor(e,r,i.options);i.options&OPT_OPEN_LEFT&&0==i.content.length&&(o|=OPT_OPEN_LEFT),this.nodes.push(new NodeContext(e,t,i.activeMarks,i.pendingMarks,n,null,o)),this.open++}},{key:"closeExtra",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}},{key:"finish",value:function(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(this.isOpen||this.options.topOpen)}},{key:"sync",value:function(e){for(var t=this.open;t>=0;t--)if(this.nodes[t]==e)return this.open=t,!0;return!1}},{key:"currentPos",get:function(){this.closeExtra();for(var e=0,t=this.open;t>=0;t--){for(var n=this.nodes[t].content,r=n.length-1;r>=0;r--)e+=n[r].nodeSize;t&&e++}return e}},{key:"findAtPoint",value:function(e,t){if(this.find)for(var n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}},{key:"findInside",value:function(e){if(this.find)for(var t=0;t<this.find.length;t++)null==this.find[t].pos&&1==e.nodeType&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}},{key:"findAround",value:function(e,t,n){if(e!=t&&this.find)for(var r=0;r<this.find.length;r++){if(null==this.find[r].pos&&1==e.nodeType&&e.contains(this.find[r].node))t.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}}},{key:"findInText",value:function(e){if(this.find)for(var t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}},{key:"matchesContext",value:function(e){var t=this;if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);var n=e.split("/"),r=this.options.context,i=!this.isOpen&&(!r||r.parent.type==this.nodes[0].type),o=-(r?r.depth+1:0)+(i?0:1);return function e(s,a){for(;s>=0;s--){var h=n[s];if(""==h){if(s==n.length-1||0==s)continue;for(;a>=o;a--)if(e(s-1,a))return!0;return!1}var u=a>0||0==a&&i?t.nodes[a].type:r&&a>=o?r.node(a-o).type:null;if(!u||u.name!=h&&-1==u.groups.indexOf(h))return!1;a--}return!0}(n.length-1,this.open)}},{key:"textblockFromContext",value:function(){var e=this.options.context;if(e)for(var t=e.depth;t>=0;t--){var n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(var r in this.parser.schema.nodes){var i=this.parser.schema.nodes[r];if(i.isTextblock&&i.defaultAttrs)return i}}},{key:"addPendingMark",value:function(e){var t=findSameMarkInSet(e,this.top.pendingMarks);t&&this.top.stashMarks.push(t),this.top.pendingMarks=e.addToSet(this.top.pendingMarks)}},{key:"removePendingMark",value:function(e,t){for(var n=this.open;n>=0;n--){var r=this.nodes[n];if(r.pendingMarks.lastIndexOf(e)>-1)r.pendingMarks=e.removeFromSet(r.pendingMarks);else{r.activeMarks=e.removeFromSet(r.activeMarks);var i=r.popFromStashMark(e);i&&r.type&&r.type.allowsMarkType(i.type)&&(r.activeMarks=i.addToSet(r.activeMarks))}if(r==t)break}}}]),e}();function normalizeList(e){for(var t=e.firstChild,n=null;t;t=t.nextSibling){var r=1==t.nodeType?t.nodeName.toLowerCase():null;r&&listTags.hasOwnProperty(r)&&n?(n.appendChild(t),t=n):"li"==r?n=t:r&&(n=null)}}function matches(e,t){return(e.matches||e.msMatchesSelector||e.webkitMatchesSelector||e.mozMatchesSelector).call(e,t)}function parseStyles(e){for(var t,n=/\s*([\w-]+)\s*:\s*([^;]+)/g,r=[];t=n.exec(e);)r.push(t[1],t[2].trim());return r}function copy(e){var t={};for(var n in e)t[n]=e[n];return t}function markMayApply(e,t){var n=t.schema.nodes,r=function(r){var i=n[r];if(!i.allowsMarkType(e))return"continue";var o=[];return function e(n){o.push(n);for(var r=0;r<n.edgeCount;r++){var i=n.edge(r),s=i.type,a=i.next;if(s==t)return!0;if(o.indexOf(a)<0&&e(a))return!0}}(i.contentMatch)?{v:!0}:void 0};for(var i in n){var o=r(i);if("continue"!==o&&"object"===_typeof(o))return o.v}}function findSameMarkInSet(e,t){for(var n=0;n<t.length;n++)if(e.eq(t[n]))return t[n]}var DOMSerializer=function(){function e(t,n){_classCallCheck(this,e),this.nodes=t,this.marks=n}return _createClass(e,[{key:"serializeFragment",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0;r||(r=doc(n).createDocumentFragment());var i=r,o=[];return e.forEach((function(e){if(o.length||e.marks.length){for(var r=0,s=0;r<o.length&&s<e.marks.length;){var a=e.marks[s];if(t.marks[a.type.name]){if(!a.eq(o[r][0])||!1===a.type.spec.spanning)break;r++,s++}else s++}for(;r<o.length;)i=o.pop()[1];for(;s<e.marks.length;){var h=e.marks[s++],u=t.serializeMark(h,e.isInline,n);u&&(o.push([h,i]),i.appendChild(u.dom),i=u.contentDOM||u.dom)}}i.appendChild(t.serializeNodeInner(e,n))})),r}},{key:"serializeNodeInner",value:function(t,n){var r=e.renderSpec(doc(n),this.nodes[t.type.name](t)),i=r.dom,o=r.contentDOM;if(o){if(t.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(t.content,n,o)}return i}},{key:"serializeNode",value:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.serializeNodeInner(e,t),r=e.marks.length-1;r>=0;r--){var i=this.serializeMark(e.marks[r],e.isInline,t);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}},{key:"serializeMark",value:function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=this.marks[t.type.name];return i&&e.renderSpec(doc(r),i(t,n))}}],[{key:"renderSpec",value:function(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if("string"==typeof n)return{dom:t.createTextNode(n)};if(null!=n.nodeType)return{dom:n};if(n.dom&&null!=n.dom.nodeType)return n;var i,o=n[0],s=o.indexOf(" ");s>0&&(r=o.slice(0,s),o=o.slice(s+1));var a=r?t.createElementNS(r,o):t.createElement(o),h=n[1],u=1;if(h&&"object"==_typeof(h)&&null==h.nodeType&&!Array.isArray(h))for(var c in u=2,h)if(null!=h[c]){var l=c.indexOf(" ");l>0?a.setAttributeNS(c.slice(0,l),c.slice(l+1),h[c]):a.setAttribute(c,h[c])}for(var f=u;f<n.length;f++){var p=n[f];if(0===p){if(f<n.length-1||f>u)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}var d=e.renderSpec(t,p,r),v=d.dom,y=d.contentDOM;if(a.appendChild(v),y){if(i)throw new RangeError("Multiple content holes");i=y}}return{dom:a,contentDOM:i}}},{key:"fromSchema",value:function(t){return t.cached.domSerializer||(t.cached.domSerializer=new e(this.nodesFromSchema(t),this.marksFromSchema(t)))}},{key:"nodesFromSchema",value:function(e){var t=gatherToDOM(e.nodes);return t.text||(t.text=function(e){return e.text}),t}},{key:"marksFromSchema",value:function(e){return gatherToDOM(e.marks)}}]),e}();function gatherToDOM(e){var t={};for(var n in e){var r=e[n].spec.toDOM;r&&(t[n]=r)}return t}function doc(e){return e.document||window.document}exports.ContentMatch=ContentMatch,exports.DOMParser=DOMParser,exports.DOMSerializer=DOMSerializer,exports.Fragment=Fragment,exports.Mark=Mark,exports.MarkType=MarkType,exports.Node=Node,exports.NodeRange=NodeRange,exports.NodeType=NodeType,exports.ReplaceError=ReplaceError,exports.ResolvedPos=ResolvedPos,exports.Schema=Schema,exports.Slice=Slice;