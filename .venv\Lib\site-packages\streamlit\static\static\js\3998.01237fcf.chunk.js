"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[3998],{52017:(t,n,e)=>{e.d(n,{B8:()=>M,Il:()=>a,J5:()=>o,SU:()=>N,Ss:()=>Z,Ym:()=>I,ZP:()=>x,xV:()=>i});var r=e(20322);function a(){}var i=.7,o=1/i,c="\\s*([+-]?\\d+)\\s*",s="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",u="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",l=/^#([0-9a-f]{3,8})$/,h=new RegExp("^rgb\\(".concat(c,",").concat(c,",").concat(c,"\\)$")),f=new RegExp("^rgb\\(".concat(u,",").concat(u,",").concat(u,"\\)$")),g=new RegExp("^rgba\\(".concat(c,",").concat(c,",").concat(c,",").concat(s,"\\)$")),p=new RegExp("^rgba\\(".concat(u,",").concat(u,",").concat(u,",").concat(s,"\\)$")),d=new RegExp("^hsl\\(".concat(s,",").concat(u,",").concat(u,"\\)$")),w=new RegExp("^hsla\\(".concat(s,",").concat(u,",").concat(u,",").concat(s,"\\)$")),m={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function b(){return this.rgb().formatHex()}function y(){return this.rgb().formatRgb()}function x(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=l.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?v(n):3===e?new Z(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?k(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?k(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=h.exec(t))?new Z(n[1],n[2],n[3],1):(n=f.exec(t))?new Z(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=g.exec(t))?k(n[1],n[2],n[3],n[4]):(n=p.exec(t))?k(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=d.exec(t))?H(n[1],n[2]/100,n[3]/100,1):(n=w.exec(t))?H(n[1],n[2]/100,n[3]/100,n[4]):m.hasOwnProperty(t)?v(m[t]):"transparent"===t?new Z(NaN,NaN,NaN,0):null}function v(t){return new Z(t>>16&255,t>>8&255,255&t,1)}function k(t,n,e,r){return r<=0&&(t=n=e=NaN),new Z(t,n,e,r)}function N(t){return t instanceof a||(t=x(t)),t?new Z((t=t.rgb()).r,t.g,t.b,t.opacity):new Z}function M(t,n,e,r){return 1===arguments.length?N(t):new Z(t,n,e,null==r?1:r)}function Z(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function _(){return"#".concat(Y(this.r)).concat(Y(this.g)).concat(Y(this.b))}function X(){const t=q(this.opacity);return"".concat(1===t?"rgb(":"rgba(").concat(E(this.r),", ").concat(E(this.g),", ").concat(E(this.b)).concat(1===t?")":", ".concat(t,")"))}function q(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function E(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Y(t){return((t=E(t))<16?"0":"")+t.toString(16)}function H(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new S(t,n,e,r)}function R(t){if(t instanceof S)return new S(t.h,t.s,t.l,t.opacity);if(t instanceof a||(t=x(t)),!t)return new S;if(t instanceof S)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),c=NaN,s=o-i,u=(o+i)/2;return s?(c=n===o?(e-r)/s+6*(e<r):e===o?(r-n)/s+2:(n-e)/s+4,s/=u<.5?o+i:2-o-i,c*=60):s=u>0&&u<1?0:c,new S(c,s,u,t.opacity)}function I(t,n,e,r){return 1===arguments.length?R(t):new S(t,n,e,null==r?1:r)}function S(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function j(t){return(t=(t||0)%360)<0?t+360:t}function $(t){return Math.max(0,Math.min(1,t||0))}function A(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}(0,r.Z)(a,x,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:b,formatHex:b,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return R(this).formatHsl()},formatRgb:y,toString:y}),(0,r.Z)(Z,M,(0,r.l)(a,{brighter(t){return t=null==t?o:Math.pow(o,t),new Z(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?i:Math.pow(i,t),new Z(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Z(E(this.r),E(this.g),E(this.b),q(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:_,formatHex:_,formatHex8:function(){return"#".concat(Y(this.r)).concat(Y(this.g)).concat(Y(this.b)).concat(Y(255*(isNaN(this.opacity)?1:this.opacity)))},formatRgb:X,toString:X})),(0,r.Z)(S,I,(0,r.l)(a,{brighter(t){return t=null==t?o:Math.pow(o,t),new S(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?i:Math.pow(i,t),new S(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,a=2*e-r;return new Z(A(t>=240?t-240:t+120,a,r),A(t,a,r),A(t<120?t+240:t-120,a,r),this.opacity)},clamp(){return new S(j(this.h),$(this.s),$(this.l),q(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=q(this.opacity);return"".concat(1===t?"hsl(":"hsla(").concat(j(this.h),", ").concat(100*$(this.s),"%, ").concat(100*$(this.l),"%").concat(1===t?")":", ".concat(t,")"))}}))},20322:(t,n,e)=>{function r(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function a(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}e.d(n,{Z:()=>r,l:()=>a})},5535:(t,n,e)=>{function r(t,n,e,r,a){var i=t*t,o=i*t;return((1-3*t+3*i-o)*n+(4-6*i+3*o)*e+(1+3*t+3*i-3*o)*r+o*a)/6}function a(t){var n=t.length-1;return function(e){var a=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[a],o=t[a+1],c=a>0?t[a-1]:2*i-o,s=a<n-1?t[a+2]:2*o-i;return r((e-a/n)*n,c,i,o,s)}}e.d(n,{Z:()=>a,t:()=>r})},20943:(t,n,e)=>{e.d(n,{Z:()=>a});var r=e(5535);function a(t){var n=t.length;return function(e){var a=Math.floor(((e%=1)<0?++e:e)*n),i=t[(a+n-1)%n],o=t[a%n],c=t[(a+1)%n],s=t[(a+2)%n];return(0,r.t)((e-a/n)*n,i,o,c,s)}}},97017:(t,n,e)=>{e.d(n,{ZP:()=>c,wx:()=>i,yi:()=>o});var r=e(31874);function a(t,n){return function(e){return t+e*n}}function i(t,n){var e=n-t;return e?a(t,e>180||e<-180?e-360*Math.round(e/360):e):(0,r.Z)(isNaN(t)?n:t)}function o(t){return 1===(t=+t)?c:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):(0,r.Z)(isNaN(n)?e:n)}}function c(t,n){var e=n-t;return e?a(t,e):(0,r.Z)(isNaN(t)?n:t)}},31874:(t,n,e)=>{e.d(n,{Z:()=>r});const r=t=>()=>t},74552:(t,n,e)=>{function r(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}e.d(n,{Z:()=>r})},30975:(t,n,e)=>{e.d(n,{YD:()=>l,ZP:()=>c,hD:()=>u});var r=e(52017),a=e(5535),i=e(20943),o=e(97017);const c=function t(n){var e=(0,o.yi)(n);function a(t,n){var a=e((t=(0,r.B8)(t)).r,(n=(0,r.B8)(n)).r),i=e(t.g,n.g),c=e(t.b,n.b),s=(0,o.ZP)(t.opacity,n.opacity);return function(n){return t.r=a(n),t.g=i(n),t.b=c(n),t.opacity=s(n),t+""}}return a.gamma=t,a}(1);function s(t){return function(n){var e,a,i=n.length,o=new Array(i),c=new Array(i),s=new Array(i);for(e=0;e<i;++e)a=(0,r.B8)(n[e]),o[e]=a.r||0,c[e]=a.g||0,s[e]=a.b||0;return o=t(o),c=t(c),s=t(s),a.opacity=1,function(t){return a.r=o(t),a.g=c(t),a.b=s(t),a+""}}}var u=s(a.Z),l=s(i.Z)},71413:(t,n,e)=>{e.d(n,{Z:()=>o});var r=e(74552),a=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,i=new RegExp(a.source,"g");function o(t,n){var e,o,c,s=a.lastIndex=i.lastIndex=0,u=-1,l=[],h=[];for(t+="",n+="";(e=a.exec(t))&&(o=i.exec(n));)(c=o.index)>s&&(c=n.slice(s,c),l[u]?l[u]+=c:l[++u]=c),(e=e[0])===(o=o[0])?l[u]?l[u]+=o:l[++u]=o:(l[++u]=null,h.push({i:u,x:(0,r.Z)(e,o)})),s=i.lastIndex;return s<n.length&&(c=n.slice(s),l[u]?l[u]+=c:l[++u]=c),l.length<2?h[0]?function(t){return function(n){return t(n)+""}}(h[0].x):function(t){return function(){return t}}(n):(n=h.length,function(t){for(var e,r=0;r<n;++r)l[(e=h[r]).i]=e.x(t);return l.join("")})}},18959:(t,n,e)=>{e.d(n,{Y:()=>u,w:()=>l});var r,a=e(74552),i=180/Math.PI,o={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function c(t,n,e,r,a,o){var c,s,u;return(c=Math.sqrt(t*t+n*n))&&(t/=c,n/=c),(u=t*e+n*r)&&(e-=t*u,r-=n*u),(s=Math.sqrt(e*e+r*r))&&(e/=s,r/=s,u/=s),t*r<n*e&&(t=-t,n=-n,u=-u,c=-c),{translateX:a,translateY:o,rotate:Math.atan2(n,t)*i,skewX:Math.atan(u)*i,scaleX:c,scaleY:s}}function s(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,c){var s=[],u=[];return o=t(o),c=t(c),function(t,r,i,o,c,s){if(t!==i||r!==o){var u=c.push("translate(",null,n,null,e);s.push({i:u-4,x:(0,a.Z)(t,i)},{i:u-2,x:(0,a.Z)(r,o)})}else(i||o)&&c.push("translate("+i+n+o+e)}(o.translateX,o.translateY,c.translateX,c.translateY,s,u),function(t,n,e,o){t!==n?(t-n>180?n+=360:n-t>180&&(t+=360),o.push({i:e.push(i(e)+"rotate(",null,r)-2,x:(0,a.Z)(t,n)})):n&&e.push(i(e)+"rotate("+n+r)}(o.rotate,c.rotate,s,u),function(t,n,e,o){t!==n?o.push({i:e.push(i(e)+"skewX(",null,r)-2,x:(0,a.Z)(t,n)}):n&&e.push(i(e)+"skewX("+n+r)}(o.skewX,c.skewX,s,u),function(t,n,e,r,o,c){if(t!==e||n!==r){var s=o.push(i(o)+"scale(",null,",",null,")");c.push({i:s-4,x:(0,a.Z)(t,e)},{i:s-2,x:(0,a.Z)(n,r)})}else 1===e&&1===r||o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,c.scaleX,c.scaleY,s,u),o=c=null,function(t){for(var n,e=-1,r=u.length;++e<r;)s[(n=u[e]).i]=n.x(t);return s.join("")}}}var u=s((function(t){const n=new("function"===typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?o:c(n.a,n.b,n.c,n.d,n.e,n.f)}),"px, ","px)","deg)"),l=s((function(t){return null==t?o:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",t),(t=r.transform.baseVal.consolidate())?c((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):o)}),", ",")",")")},89121:(t,n,e)=>{e.d(n,{B7:()=>w,HT:()=>m,zO:()=>p});var r,a,i=0,o=0,c=0,s=1e3,u=0,l=0,h=0,f="object"===typeof performance&&performance.now?performance:Date,g="object"===typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function p(){return l||(g(d),l=f.now()+h)}function d(){l=0}function w(){this._call=this._time=this._next=null}function m(t,n,e){var r=new w;return r.restart(t,n,e),r}function b(){l=(u=f.now())+h,i=o=0;try{!function(){p(),++i;for(var t,n=r;n;)(t=l-n._time)>=0&&n._call.call(void 0,t),n=n._next;--i}()}finally{i=0,function(){var t,n,e=r,i=1/0;for(;e;)e._call?(i>e._time&&(i=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:r=n);a=t,x(i)}(),l=0}}function y(){var t=f.now(),n=t-u;n>s&&(h-=n,u=t)}function x(t){i||(o&&(o=clearTimeout(o)),t-l>24?(t<1/0&&(o=setTimeout(b,t-f.now()-h)),c&&(c=clearInterval(c))):(c||(u=f.now(),c=setInterval(y,s)),i=1,g(b)))}w.prototype=m.prototype={constructor:w,restart:function(t,n,e){if("function"!==typeof t)throw new TypeError("callback is not a function");e=(null==e?p():+e)+(null==n?0:+n),this._next||a===this||(a?a._next=this:r=this,a=this),this._call=t,this._time=e,x()},stop:function(){this._call&&(this._call=null,this._time=1/0,x())}}}}]);