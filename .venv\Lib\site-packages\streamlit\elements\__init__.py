# Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2024)
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

WIDGETS = [
    "button",
    "camera_input",
    "chat_input",
    "checkbox",
    "color_picker",
    "component_instance",
    "download_button",
    "file_uploader",
    "form",
    "multiselect",
    "number_input",
    "radio",
    "selectbox",
    "select_slider",
    "slider",
    "text_input",
    "text_area",
    "time_input",
    "date_input",
]
NONWIDGET_ELEMENTS = [
    "alert",
    "arrow_area_chart",
    "arrow_bar_chart",
    "arrow_data_frame",
    "arrow_line_chart",
    "arrow_table",
    "arrow_vega_lite_chart",
    "audio",
    "balloons",
    "bokeh_chart",
    "code",
    "deck_gl_json_chart",
    "doc_string",
    "empty",
    "exception",
    "graphviz_chart",
    "heading",
    "iframe",
    "imgs",
    "json",
    # link_button unlike button and download_button is not a widget. It only sends a
    # forward messages to the frontend, and not sends trigger value back.
    "link_button",
    "markdown",
    "metric",
    "page_link",
    "plotly_chart",
    "progress",
    "pyplot",
    "snow",
    "text",
    "video",
    "write",
]
