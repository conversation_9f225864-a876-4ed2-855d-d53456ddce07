# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/ChatInput.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fstreamlit/proto/ChatInput.proto\"\xc3\x01\n\tChatInput\x12\n\n\x02id\x18\x01 \x01(\t\x12\x13\n\x0bplaceholder\x18\x02 \x01(\t\x12\x11\n\tmax_chars\x18\x03 \x01(\r\x12\x10\n\x08\x64isabled\x18\x04 \x01(\x08\x12\r\n\x05value\x18\x05 \x01(\t\x12\x11\n\tset_value\x18\x06 \x01(\x08\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\x07 \x01(\t\x12%\n\x08position\x18\x08 \x01(\x0e\x32\x13.ChatInput.Position\"\x16\n\x08Position\x12\n\n\x06\x42OTTOM\x10\x00\x42.\n\x1c\x63om.snowflake.apps.streamlitB\x0e\x43hatInputProtob\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.ChatInput_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\034com.snowflake.apps.streamlitB\016ChatInputProto'
  _CHATINPUT._serialized_start=36
  _CHATINPUT._serialized_end=231
  _CHATINPUT_POSITION._serialized_start=209
  _CHATINPUT_POSITION._serialized_end=231
# @@protoc_insertion_point(module_scope)
