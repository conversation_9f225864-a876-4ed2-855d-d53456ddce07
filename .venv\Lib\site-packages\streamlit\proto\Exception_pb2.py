# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/Exception.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fstreamlit/proto/Exception.proto\"p\n\tException\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1b\n\x13message_is_markdown\x18\x04 \x01(\x08\x12\x13\n\x0bstack_trace\x18\x03 \x03(\t\x12\x12\n\nis_warning\x18\x05 \x01(\x08\x42.\n\x1c\x63om.snowflake.apps.streamlitB\x0e\x45xceptionProtob\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.Exception_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\034com.snowflake.apps.streamlitB\016ExceptionProto'
  _EXCEPTION._serialized_start=35
  _EXCEPTION._serialized_end=147
# @@protoc_insertion_point(module_scope)
