# Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2024)
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
from __future__ import annotations

from dataclasses import dataclass
from textwrap import dedent
from typing import TYPE_CHECKING, Any, Callable, Generic, Sequence, cast

from streamlit.elements.form import current_form_id
from streamlit.elements.utils import (
    check_callback_rules,
    check_session_state_rules,
    get_label_visibility_proto_value,
    maybe_coerce_enum,
)
from streamlit.errors import StreamlitAPIException
from streamlit.proto.Selectbox_pb2 import Selectbox as SelectboxProto
from streamlit.runtime.metrics_util import gather_metrics
from streamlit.runtime.scriptrunner import ScriptRun<PERSON>ontext, get_script_run_ctx
from streamlit.runtime.state import (
    WidgetArgs,
    WidgetCallback,
    WidgetKwargs,
    register_widget,
)
from streamlit.runtime.state.common import compute_widget_id
from streamlit.type_util import (
    Key,
    LabelVisibility,
    OptionSequence,
    T,
    check_python_comparable,
    ensure_indexable,
    maybe_raise_label_warnings,
    to_key,
)
from streamlit.util import index_

if TYPE_CHECKING:
    from streamlit.delta_generator import DeltaGenerator


@dataclass
class SelectboxSerde(Generic[T]):
    options: Sequence[T]
    index: int | None

    def serialize(self, v: object) -> int | None:
        if v is None:
            return None
        if len(self.options) == 0:
            return 0
        return index_(self.options, v)

    def deserialize(
        self,
        ui_value: int | None,
        widget_id: str = "",
    ) -> T | None:
        idx = ui_value if ui_value is not None else self.index
        return self.options[idx] if idx is not None and len(self.options) > 0 else None


class SelectboxMixin:
    @gather_metrics("selectbox")
    def selectbox(
        self,
        label: str,
        options: OptionSequence[T],
        index: int | None = 0,
        format_func: Callable[[Any], Any] = str,
        key: Key | None = None,
        help: str | None = None,
        on_change: WidgetCallback | None = None,
        args: WidgetArgs | None = None,
        kwargs: WidgetKwargs | None = None,
        *,  # keyword-only arguments:
        placeholder: str = "Choose an option",
        disabled: bool = False,
        label_visibility: LabelVisibility = "visible",
    ) -> T | None:
        r"""Display a select widget.

        Parameters
        ----------
        label : str
            A short label explaining to the user what this select widget is for.
            The label can optionally contain Markdown and supports the following
            elements: Bold, Italics, Strikethroughs, Inline Code, Emojis, and Links.

            This also supports:

            * Emoji shortcodes, such as ``:+1:``  and ``:sunglasses:``.
              For a list of all supported codes,
              see https://share.streamlit.io/streamlit/emoji-shortcodes.

            * LaTeX expressions, by wrapping them in "$" or "$$" (the "$$"
              must be on their own lines). Supported LaTeX functions are listed
              at https://katex.org/docs/supported.html.

            * Colored text, using the syntax ``:color[text to be colored]``,
              where ``color`` needs to be replaced with any of the following
              supported colors: blue, green, orange, red, violet, gray/grey, rainbow.

            Unsupported elements are unwrapped so only their children (text contents) render.
            Display unsupported elements as literal characters by
            backslash-escaping them. E.g. ``1\. Not an ordered list``.

            For accessibility reasons, you should never set an empty label (label="")
            but hide it with label_visibility if needed. In the future, we may disallow
            empty labels by raising an exception.
        options : Iterable
            Labels for the select options in an Iterable. For example, this can
            be a list, numpy.ndarray, pandas.Series, pandas.DataFrame, or
            pandas.Index. For pandas.DataFrame, the first column is used.
            Each label will be cast to str internally by default.
        index : int
            The index of the preselected option on first render. If ``None``,
            will initialize empty and return ``None`` until the user selects an option.
            Defaults to 0 (the first option).
        format_func : function
            Function to modify the display of the labels. It receives the option
            as an argument and its output will be cast to str.
        key : str or int
            An optional string or integer to use as the unique key for the widget.
            If this is omitted, a key will be generated for the widget
            based on its content. Multiple widgets of the same type may
            not share the same key.
        help : str
            An optional tooltip that gets displayed next to the selectbox.
        on_change : callable
            An optional callback invoked when this selectbox's value changes.
        args : tuple
            An optional tuple of args to pass to the callback.
        kwargs : dict
            An optional dict of kwargs to pass to the callback.
        placeholder : str
            A string to display when no options are selected.
            Defaults to 'Choose an option'.
        disabled : bool
            An optional boolean, which disables the selectbox if set to True.
            The default is False.
        label_visibility : "visible", "hidden", or "collapsed"
            The visibility of the label. If "hidden", the label doesn't show but there
            is still empty space for it above the widget (equivalent to label="").
            If "collapsed", both the label and the space are removed. Default is
            "visible".

        Returns
        -------
        any
            The selected option or ``None`` if no option is selected.

        Example
        -------
        >>> import streamlit as st
        >>>
        >>> option = st.selectbox(
        ...     'How would you like to be contacted?',
        ...     ('Email', 'Home phone', 'Mobile phone'))
        >>>
        >>> st.write('You selected:', option)

        .. output::
           https://doc-selectbox.streamlit.app/
           height: 320px

        To initialize an empty selectbox, use ``None`` as the index value:

        >>> import streamlit as st
        >>>
        >>> option = st.selectbox(
        ...    "How would you like to be contacted?",
        ...    ("Email", "Home phone", "Mobile phone"),
        ...    index=None,
        ...    placeholder="Select contact method...",
        ... )
        >>>
        >>> st.write('You selected:', option)

        .. output::
           https://doc-selectbox-empty.streamlit.app/
           height: 320px

        """
        ctx = get_script_run_ctx()
        return self._selectbox(
            label=label,
            options=options,
            index=index,
            format_func=format_func,
            key=key,
            help=help,
            on_change=on_change,
            args=args,
            kwargs=kwargs,
            placeholder=placeholder,
            disabled=disabled,
            label_visibility=label_visibility,
            ctx=ctx,
        )

    def _selectbox(
        self,
        label: str,
        options: OptionSequence[T],
        index: int | None = 0,
        format_func: Callable[[Any], Any] = str,
        key: Key | None = None,
        help: str | None = None,
        on_change: WidgetCallback | None = None,
        args: WidgetArgs | None = None,
        kwargs: WidgetKwargs | None = None,
        *,  # keyword-only arguments:
        placeholder: str = "Choose an option",
        disabled: bool = False,
        label_visibility: LabelVisibility = "visible",
        ctx: ScriptRunContext | None = None,
    ) -> T | None:
        key = to_key(key)
        check_callback_rules(self.dg, on_change)
        check_session_state_rules(default_value=None if index == 0 else index, key=key)

        maybe_raise_label_warnings(label, label_visibility)

        opt = ensure_indexable(options)
        check_python_comparable(opt)

        id = compute_widget_id(
            "selectbox",
            user_key=key,
            label=label,
            options=[str(format_func(option)) for option in opt],
            index=index,
            key=key,
            help=help,
            placeholder=placeholder,
            form_id=current_form_id(self.dg),
            page=ctx.page_script_hash if ctx else None,
        )

        if not isinstance(index, int) and index is not None:
            raise StreamlitAPIException(
                "Selectbox Value has invalid type: %s" % type(index).__name__
            )

        if index is not None and len(opt) > 0 and not 0 <= index < len(opt):
            raise StreamlitAPIException(
                "Selectbox index must be between 0 and length of options"
            )

        selectbox_proto = SelectboxProto()
        selectbox_proto.id = id
        selectbox_proto.label = label
        if index is not None:
            selectbox_proto.default = index
        selectbox_proto.options[:] = [str(format_func(option)) for option in opt]
        selectbox_proto.form_id = current_form_id(self.dg)
        selectbox_proto.placeholder = placeholder
        selectbox_proto.disabled = disabled
        selectbox_proto.label_visibility.value = get_label_visibility_proto_value(
            label_visibility
        )

        if help is not None:
            selectbox_proto.help = dedent(help)

        serde = SelectboxSerde(opt, index)

        widget_state = register_widget(
            "selectbox",
            selectbox_proto,
            user_key=key,
            on_change_handler=on_change,
            args=args,
            kwargs=kwargs,
            deserializer=serde.deserialize,
            serializer=serde.serialize,
            ctx=ctx,
        )
        widget_state = maybe_coerce_enum(widget_state, options, opt)

        if widget_state.value_changed:
            serialized_value = serde.serialize(widget_state.value)
            if serialized_value is not None:
                selectbox_proto.value = serialized_value
            selectbox_proto.set_value = True

        self.dg._enqueue("selectbox", selectbox_proto)
        return widget_state.value

    @property
    def dg(self) -> "DeltaGenerator":
        """Get our DeltaGenerator."""
        return cast("DeltaGenerator", self)
