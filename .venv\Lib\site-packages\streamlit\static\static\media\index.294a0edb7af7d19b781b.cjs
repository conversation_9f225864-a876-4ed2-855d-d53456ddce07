"use strict";function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}function _get(){return _get="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=_superPropBase(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}},_get.apply(this,arguments)}function _superPropBase(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=_getPrototypeOf(e)););return e}function _inherits(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return _setPrototypeOf=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},_setPrototypeOf(e,t)}function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var n,r=_getPrototypeOf(e);if(t){var o=_getPrototypeOf(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return _possibleConstructorReturn(this,n)}}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function _getPrototypeOf(e){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},_getPrototypeOf(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}Object.defineProperty(exports,"__esModule",{value:!0});var prosemirrorModel=require("prosemirror-model"),prosemirrorTransform=require("prosemirror-transform"),classesById=Object.create(null),Selection=function(){function e(t,n,r){_classCallCheck(this,e),this.$anchor=t,this.$head=n,this.ranges=r||[new SelectionRange(t.min(n),t.max(n))]}return _createClass(e,[{key:"anchor",get:function(){return this.$anchor.pos}},{key:"head",get:function(){return this.$head.pos}},{key:"from",get:function(){return this.$from.pos}},{key:"to",get:function(){return this.$to.pos}},{key:"$from",get:function(){return this.ranges[0].$from}},{key:"$to",get:function(){return this.ranges[0].$to}},{key:"empty",get:function(){for(var e=this.ranges,t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}},{key:"content",value:function(){return this.$from.doc.slice(this.from,this.to,!0)}},{key:"replace",value:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:prosemirrorModel.Slice.empty,n=t.content.lastChild,r=null,o=0;o<t.openEnd;o++)r=n,n=n.lastChild;for(var i=e.steps.length,s=this.ranges,a=0;a<s.length;a++){var c=s[a],l=c.$from,u=c.$to,f=e.mapping.slice(i);e.replaceRange(f.map(l.pos),f.map(u.pos),a?prosemirrorModel.Slice.empty:t),0==a&&selectionToInsertionEnd(e,i,(n?n.isInline:r&&r.isTextblock)?-1:1)}}},{key:"replaceWith",value:function(e,t){for(var n=e.steps.length,r=this.ranges,o=0;o<r.length;o++){var i=r[o],s=i.$from,a=i.$to,c=e.mapping.slice(n),l=c.map(s.pos),u=c.map(a.pos);o?e.deleteRange(l,u):(e.replaceRangeWith(l,u,t),selectionToInsertionEnd(e,n,t.isInline?-1:1))}}},{key:"getBookmark",value:function(){return TextSelection.between(this.$anchor,this.$head).getBookmark()}}],[{key:"findFrom",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e.parent.inlineContent?new TextSelection(e):findSelectionIn(e.node(0),e.parent,e.pos,e.index(),t,n);if(r)return r;for(var o=e.depth-1;o>=0;o--){var i=t<0?findSelectionIn(e.node(0),e.node(o),e.before(o+1),e.index(o),t,n):findSelectionIn(e.node(0),e.node(o),e.after(o+1),e.index(o)+1,t,n);if(i)return i}return null}},{key:"near",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return this.findFrom(e,t)||this.findFrom(e,-t)||new AllSelection(e.node(0))}},{key:"atStart",value:function(e){return findSelectionIn(e,e,0,0,1)||new AllSelection(e)}},{key:"atEnd",value:function(e){return findSelectionIn(e,e,e.content.size,e.childCount,-1)||new AllSelection(e)}},{key:"fromJSON",value:function(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");var n=classesById[t.type];if(!n)throw new RangeError("No selection type ".concat(t.type," defined"));return n.fromJSON(e,t)}},{key:"jsonID",value:function(e,t){if(e in classesById)throw new RangeError("Duplicate use of selection JSON ID "+e);return classesById[e]=t,t.prototype.jsonID=e,t}}]),e}();Selection.prototype.visible=!0;var SelectionRange=_createClass((function e(t,n){_classCallCheck(this,e),this.$from=t,this.$to=n})),warnedAboutTextSelection=!1;function checkTextSelection(e){warnedAboutTextSelection||e.parent.inlineContent||(warnedAboutTextSelection=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+e.parent.type.name+")"))}var TextSelection=function(e){_inherits(n,Selection);var t=_createSuper(n);function n(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e;return _classCallCheck(this,n),checkTextSelection(e),checkTextSelection(r),t.call(this,e,r)}return _createClass(n,[{key:"$cursor",get:function(){return this.$anchor.pos==this.$head.pos?this.$head:null}},{key:"map",value:function(e,t){var r=e.resolve(t.map(this.head));if(!r.parent.inlineContent)return Selection.near(r);var o=e.resolve(t.map(this.anchor));return new n(o.parent.inlineContent?o:r,r)}},{key:"replace",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:prosemirrorModel.Slice.empty;if(_get(_getPrototypeOf(n.prototype),"replace",this).call(this,e,t),t==prosemirrorModel.Slice.empty){var r=this.$from.marksAcross(this.$to);r&&e.ensureMarks(r)}}},{key:"eq",value:function(e){return e instanceof n&&e.anchor==this.anchor&&e.head==this.head}},{key:"getBookmark",value:function(){return new TextBookmark(this.anchor,this.head)}},{key:"toJSON",value:function(){return{type:"text",anchor:this.anchor,head:this.head}}}],[{key:"fromJSON",value:function(e,t){if("number"!=typeof t.anchor||"number"!=typeof t.head)throw new RangeError("Invalid input for TextSelection.fromJSON");return new n(e.resolve(t.anchor),e.resolve(t.head))}},{key:"create",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=e.resolve(t);return new this(r,n==t?r:e.resolve(n))}},{key:"between",value:function(e,t,r){var o=e.pos-t.pos;if(r&&!o||(r=o>=0?1:-1),!t.parent.inlineContent){var i=Selection.findFrom(t,r,!0)||Selection.findFrom(t,-r,!0);if(!i)return Selection.near(t,r);t=i.$head}return e.parent.inlineContent||(0==o||(e=(Selection.findFrom(e,-r,!0)||Selection.findFrom(e,r,!0)).$anchor).pos<t.pos!=o<0)&&(e=t),new n(e,t)}}]),n}();Selection.jsonID("text",TextSelection);var TextBookmark=function(){function e(t,n){_classCallCheck(this,e),this.anchor=t,this.head=n}return _createClass(e,[{key:"map",value:function(t){return new e(t.map(this.anchor),t.map(this.head))}},{key:"resolve",value:function(e){return TextSelection.between(e.resolve(this.anchor),e.resolve(this.head))}}]),e}(),NodeSelection=function(e){_inherits(n,Selection);var t=_createSuper(n);function n(e){var r;_classCallCheck(this,n);var o=e.nodeAfter,i=e.node(0).resolve(e.pos+o.nodeSize);return(r=t.call(this,e,i)).node=o,r}return _createClass(n,[{key:"map",value:function(e,t){var r=t.mapResult(this.anchor),o=r.deleted,i=r.pos,s=e.resolve(i);return o?Selection.near(s):new n(s)}},{key:"content",value:function(){return new prosemirrorModel.Slice(prosemirrorModel.Fragment.from(this.node),0,0)}},{key:"eq",value:function(e){return e instanceof n&&e.anchor==this.anchor}},{key:"toJSON",value:function(){return{type:"node",anchor:this.anchor}}},{key:"getBookmark",value:function(){return new NodeBookmark(this.anchor)}}],[{key:"fromJSON",value:function(e,t){if("number"!=typeof t.anchor)throw new RangeError("Invalid input for NodeSelection.fromJSON");return new n(e.resolve(t.anchor))}},{key:"create",value:function(e,t){return new n(e.resolve(t))}},{key:"isSelectable",value:function(e){return!e.isText&&!1!==e.type.spec.selectable}}]),n}();NodeSelection.prototype.visible=!1,Selection.jsonID("node",NodeSelection);var NodeBookmark=function(){function e(t){_classCallCheck(this,e),this.anchor=t}return _createClass(e,[{key:"map",value:function(t){var n=t.mapResult(this.anchor),r=n.deleted,o=n.pos;return r?new TextBookmark(o,o):new e(o)}},{key:"resolve",value:function(e){var t=e.resolve(this.anchor),n=t.nodeAfter;return n&&NodeSelection.isSelectable(n)?new NodeSelection(t):Selection.near(t)}}]),e}(),AllSelection=function(e){_inherits(n,Selection);var t=_createSuper(n);function n(e){return _classCallCheck(this,n),t.call(this,e.resolve(0),e.resolve(e.content.size))}return _createClass(n,[{key:"replace",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:prosemirrorModel.Slice.empty;if(t==prosemirrorModel.Slice.empty){e.delete(0,e.doc.content.size);var r=Selection.atStart(e.doc);r.eq(e.selection)||e.setSelection(r)}else _get(_getPrototypeOf(n.prototype),"replace",this).call(this,e,t)}},{key:"toJSON",value:function(){return{type:"all"}}},{key:"map",value:function(e){return new n(e)}},{key:"eq",value:function(e){return e instanceof n}},{key:"getBookmark",value:function(){return AllBookmark}}],[{key:"fromJSON",value:function(e){return new n(e)}}]),n}();Selection.jsonID("all",AllSelection);var AllBookmark={map:function(){return this},resolve:function(e){return new AllSelection(e)}};function findSelectionIn(e,t,n,r,o){var i=arguments.length>5&&void 0!==arguments[5]&&arguments[5];if(t.inlineContent)return TextSelection.create(e,n);for(var s=r-(o>0?0:1);o>0?s<t.childCount:s>=0;s+=o){var a=t.child(s);if(a.isAtom){if(!i&&NodeSelection.isSelectable(a))return NodeSelection.create(e,n-(o<0?a.nodeSize:0))}else{var c=findSelectionIn(e,a,n+o,o<0?a.childCount:0,o,i);if(c)return c}n+=a.nodeSize*o}return null}function selectionToInsertionEnd(e,t,n){var r=e.steps.length-1;if(!(r<t)){var o,i=e.steps[r];if(i instanceof prosemirrorTransform.ReplaceStep||i instanceof prosemirrorTransform.ReplaceAroundStep)e.mapping.maps[r].forEach((function(e,t,n,r){null==o&&(o=r)})),e.setSelection(Selection.near(e.doc.resolve(o),n))}}var UPDATED_SEL=1,UPDATED_MARKS=2,UPDATED_SCROLL=4,Transaction=function(e){_inherits(n,prosemirrorTransform.Transform);var t=_createSuper(n);function n(e){var r;return _classCallCheck(this,n),(r=t.call(this,e.doc)).curSelectionFor=0,r.updated=0,r.meta=Object.create(null),r.time=Date.now(),r.curSelection=e.selection,r.storedMarks=e.storedMarks,r}return _createClass(n,[{key:"selection",get:function(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}},{key:"setSelection",value:function(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(this.updated|UPDATED_SEL)&~UPDATED_MARKS,this.storedMarks=null,this}},{key:"selectionSet",get:function(){return(this.updated&UPDATED_SEL)>0}},{key:"setStoredMarks",value:function(e){return this.storedMarks=e,this.updated|=UPDATED_MARKS,this}},{key:"ensureMarks",value:function(e){return prosemirrorModel.Mark.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}},{key:"addStoredMark",value:function(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}},{key:"removeStoredMark",value:function(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}},{key:"storedMarksSet",get:function(){return(this.updated&UPDATED_MARKS)>0}},{key:"addStep",value:function(e,t){_get(_getPrototypeOf(n.prototype),"addStep",this).call(this,e,t),this.updated=this.updated&~UPDATED_MARKS,this.storedMarks=null}},{key:"setTime",value:function(e){return this.time=e,this}},{key:"replaceSelection",value:function(e){return this.selection.replace(this,e),this}},{key:"replaceSelectionWith",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||prosemirrorModel.Mark.none))),n.replaceWith(this,e),this}},{key:"deleteSelection",value:function(){return this.selection.replace(this),this}},{key:"insertText",value:function(e,t,n){var r=this.doc.type.schema;if(null==t)return e?this.replaceSelectionWith(r.text(e),!0):this.deleteSelection();if(null==n&&(n=t),n=null==n?t:n,!e)return this.deleteRange(t,n);var o=this.storedMarks;if(!o){var i=this.doc.resolve(t);o=n==t?i.marks():i.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,r.text(e,o)),this.selection.empty||this.setSelection(Selection.near(this.selection.$to)),this}},{key:"setMeta",value:function(e,t){return this.meta["string"==typeof e?e:e.key]=t,this}},{key:"getMeta",value:function(e){return this.meta["string"==typeof e?e:e.key]}},{key:"isGeneric",get:function(){for(var e in this.meta)return!1;return!0}},{key:"scrollIntoView",value:function(){return this.updated|=UPDATED_SCROLL,this}},{key:"scrolledIntoView",get:function(){return(this.updated&UPDATED_SCROLL)>0}}]),n}();function bind(e,t){return t&&e?e.bind(t):e}var FieldDesc=_createClass((function e(t,n,r){_classCallCheck(this,e),this.name=t,this.init=bind(n.init,r),this.apply=bind(n.apply,r)})),baseFields=[new FieldDesc("doc",{init:function(e){return e.doc||e.schema.topNodeType.createAndFill()},apply:function(e){return e.doc}}),new FieldDesc("selection",{init:function(e,t){return e.selection||Selection.atStart(t.doc)},apply:function(e){return e.selection}}),new FieldDesc("storedMarks",{init:function(e){return e.storedMarks||null},apply:function(e,t,n,r){return r.selection.$cursor?e.storedMarks:null}}),new FieldDesc("scrollToSelection",{init:function(){return 0},apply:function(e,t){return e.scrolledIntoView?t+1:t}})],Configuration=_createClass((function e(t,n){var r=this;_classCallCheck(this,e),this.schema=t,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=baseFields.slice(),n&&n.forEach((function(e){if(r.pluginsByKey[e.key])throw new RangeError("Adding different instances of a keyed plugin ("+e.key+")");r.plugins.push(e),r.pluginsByKey[e.key]=e,e.spec.state&&r.fields.push(new FieldDesc(e.key,e.spec.state,e))}))})),EditorState=function(){function e(t){_classCallCheck(this,e),this.config=t}return _createClass(e,[{key:"schema",get:function(){return this.config.schema}},{key:"plugins",get:function(){return this.config.plugins}},{key:"apply",value:function(e){return this.applyTransaction(e).state}},{key:"filterTransaction",value:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1,n=0;n<this.config.plugins.length;n++)if(n!=t){var r=this.config.plugins[n];if(r.spec.filterTransaction&&!r.spec.filterTransaction.call(r,e,this))return!1}return!0}},{key:"applyTransaction",value:function(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};for(var t=[e],n=this.applyInner(e),r=null;;){for(var o=!1,i=0;i<this.config.plugins.length;i++){var s=this.config.plugins[i];if(s.spec.appendTransaction){var a=r?r[i].n:0,c=r?r[i].state:this,l=a<t.length&&s.spec.appendTransaction.call(s,a?t.slice(a):t,c,n);if(l&&n.filterTransaction(l,i)){if(l.setMeta("appendedTransaction",e),!r){r=[];for(var u=0;u<this.config.plugins.length;u++)r.push(u<i?{state:n,n:t.length}:{state:this,n:0})}t.push(l),n=n.applyInner(l),o=!0}r&&(r[i]={state:n,n:t.length})}}if(!o)return{state:n,transactions:t}}}},{key:"applyInner",value:function(t){if(!t.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");for(var n=new e(this.config),r=this.config.fields,o=0;o<r.length;o++){var i=r[o];n[i.name]=i.apply(t,this[i.name],this,n)}return n}},{key:"tr",get:function(){return new Transaction(this)}},{key:"reconfigure",value:function(t){for(var n=new Configuration(this.schema,t.plugins),r=n.fields,o=new e(n),i=0;i<r.length;i++){var s=r[i].name;o[s]=this.hasOwnProperty(s)?this[s]:r[i].init(t,o)}return o}},{key:"toJSON",value:function(e){var t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map((function(e){return e.toJSON()}))),e&&"object"==_typeof(e))for(var n in e){if("doc"==n||"selection"==n)throw new RangeError("The JSON fields `doc` and `selection` are reserved");var r=e[n],o=r.spec.state;o&&o.toJSON&&(t[n]=o.toJSON.call(r,this[r.key]))}return t}}],[{key:"create",value:function(t){for(var n=new Configuration(t.doc?t.doc.type.schema:t.schema,t.plugins),r=new e(n),o=0;o<n.fields.length;o++)r[n.fields[o].name]=n.fields[o].init(t,r);return r}},{key:"fromJSON",value:function(t,n,r){if(!n)throw new RangeError("Invalid input for EditorState.fromJSON");if(!t.schema)throw new RangeError("Required config field 'schema' missing");var o=new Configuration(t.schema,t.plugins),i=new e(o);return o.fields.forEach((function(e){if("doc"==e.name)i.doc=prosemirrorModel.Node.fromJSON(t.schema,n.doc);else if("selection"==e.name)i.selection=Selection.fromJSON(i.doc,n.selection);else if("storedMarks"==e.name)n.storedMarks&&(i.storedMarks=n.storedMarks.map(t.schema.markFromJSON));else{if(r)for(var o in r){var s=r[o],a=s.spec.state;if(s.key==e.name&&a&&a.fromJSON&&Object.prototype.hasOwnProperty.call(n,o))return void(i[e.name]=a.fromJSON.call(s,t,n[o],i))}i[e.name]=e.init(t,i)}})),i}}]),e}();function bindProps(e,t,n){for(var r in e){var o=e[r];o instanceof Function?o=o.bind(t):"handleDOMEvents"==r&&(o=bindProps(o,t,{})),n[r]=o}return n}var Plugin=function(){function e(t){_classCallCheck(this,e),this.spec=t,this.props={},t.props&&bindProps(t.props,this,this.props),this.key=t.key?t.key.key:createKey("plugin")}return _createClass(e,[{key:"getState",value:function(e){return e[this.key]}}]),e}(),keys=Object.create(null);function createKey(e){return e in keys?e+"$"+ ++keys[e]:(keys[e]=0,e+"$")}var PluginKey=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"key";_classCallCheck(this,e),this.key=createKey(t)}return _createClass(e,[{key:"get",value:function(e){return e.config.pluginsByKey[this.key]}},{key:"getState",value:function(e){return e[this.key]}}]),e}();exports.AllSelection=AllSelection,exports.EditorState=EditorState,exports.NodeSelection=NodeSelection,exports.Plugin=Plugin,exports.PluginKey=PluginKey,exports.Selection=Selection,exports.SelectionRange=SelectionRange,exports.TextSelection=TextSelection,exports.Transaction=Transaction;