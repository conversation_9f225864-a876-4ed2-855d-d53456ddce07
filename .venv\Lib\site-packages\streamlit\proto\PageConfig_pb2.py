# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/PageConfig.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n streamlit/proto/PageConfig.proto\"\x97\x03\n\nPageConfig\x12\r\n\x05title\x18\x01 \x01(\t\x12\x0f\n\x07\x66\x61vicon\x18\x02 \x01(\t\x12\"\n\x06layout\x18\x03 \x01(\x0e\x32\x12.PageConfig.Layout\x12\x37\n\x15initial_sidebar_state\x18\x04 \x01(\x0e\x32\x18.PageConfig.SidebarState\x12)\n\nmenu_items\x18\x05 \x01(\x0b\x32\x15.PageConfig.MenuItems\x1a\x87\x01\n\tMenuItems\x12\x14\n\x0cget_help_url\x18\x01 \x01(\t\x12\x15\n\rhide_get_help\x18\x02 \x01(\x08\x12\x18\n\x10report_a_bug_url\x18\x03 \x01(\t\x12\x19\n\x11hide_report_a_bug\x18\x04 \x01(\x08\x12\x18\n\x10\x61\x62out_section_md\x18\x05 \x01(\t\" \n\x06Layout\x12\x0c\n\x08\x43\x45NTERED\x10\x00\x12\x08\n\x04WIDE\x10\x01\"5\n\x0cSidebarState\x12\x08\n\x04\x41UTO\x10\x00\x12\x0c\n\x08\x45XPANDED\x10\x01\x12\r\n\tCOLLAPSED\x10\x02\x42/\n\x1c\x63om.snowflake.apps.streamlitB\x0fPageConfigProtob\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.PageConfig_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\034com.snowflake.apps.streamlitB\017PageConfigProto'
  _PAGECONFIG._serialized_start=37
  _PAGECONFIG._serialized_end=444
  _PAGECONFIG_MENUITEMS._serialized_start=220
  _PAGECONFIG_MENUITEMS._serialized_end=355
  _PAGECONFIG_LAYOUT._serialized_start=357
  _PAGECONFIG_LAYOUT._serialized_end=389
  _PAGECONFIG_SIDEBARSTATE._serialized_start=391
  _PAGECONFIG_SIDEBARSTATE._serialized_end=444
# @@protoc_insertion_point(module_scope)
