/*
object-assign
(c) <PERSON><PERSON>
@license MIT
*/

/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/

/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> <https://feross.org>
 * @license  MIT
 */

/*!
 * XRegExp 4.0.0
 * <xregexp.com>
 * <PERSON> (c) 2007-2017 MIT License
 */

/*!
 * XRegExp Unicode Base 4.0.0
 * <xregexp.com>
 * <PERSON> (c) 2008-2017 MIT License
 */

/*!
 * XRegExp Unicode Blocks 4.0.0
 * <xregexp.com>
 * <PERSON> (c) 2010-2017 MIT License
 * Unicode data by <PERSON> <mathiasbynens.be>
 */

/*!
 * XRegExp Unicode Categories 4.0.0
 * <xregexp.com>
 * <PERSON> (c) 2010-2017 MIT License
 * Unicode data by <PERSON> <mathiasbynens.be>
 */

/*!
 * XRegExp Unicode Properties 4.0.0
 * <xregexp.com>
 * <PERSON> (c) 2012-2017 MIT License
 * Unicode data by <PERSON> <mathiasbynens.be>
 */

/*!
 * XRegExp Unicode Scripts 4.0.0
 * <xregexp.com>
 * <PERSON> Levithan (c) 2010-2017 MIT License
 * Unicode data by Mathias Bynens <mathiasbynens.be>
 */

/*!
 * XRegExp.build 4.0.0
 * <xregexp.com>
 * <PERSON>than (c) 2012-2017 MIT License
 */

/*!
 * XRegExp.matchRecursive 4.0.0
 * <xregexp.com>
 * Steven Levithan (c) 2009-2017 MIT License
 */

/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */

/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */

/** @license React v0.20.2
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/** @license React v17.0.2
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/** @license React v17.0.2
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/** @license React v17.0.2
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

//! Copyright (c) JS Foundation and other contributors

//! github.com/moment/moment-timezone

//! license : MIT

//! moment-timezone.js

//! moment.js

//! version : 0.5.40
