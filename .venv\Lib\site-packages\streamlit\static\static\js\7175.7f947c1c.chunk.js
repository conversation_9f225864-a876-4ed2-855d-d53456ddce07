"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[7175],{79986:(t,e,i)=>{i.d(e,{Z:()=>c});i(66845);var s,r=i(50641),o=i(86659),n=i(50669),a=i(1515);const l=(0,i(7865).F4)(s||(s=(0,n.Z)(["\n  50% {\n    color: rgba(0, 0, 0, 0);\n  }\n"]))),d=(0,a.Z)("span",{target:"edlqvik0"})((t=>{let{includeDot:e,shouldBlink:i,theme:s}=t;return{...e?{"&::before":{opacity:1,content:'"\u2022"',animation:"none",color:s.colors.gray,margin:"0 5px"}}:{},...i?{color:s.colors.red,animationName:"".concat(l),animationDuration:"0.5s",animationIterationCount:5}:{}}}),"");var u=i(40864);const c=t=>{let{dirty:e,value:i,maxLength:s,className:n,type:a="single",inForm:l}=t;const c=[],h=function(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];c.push((0,u.jsx)(d,{includeDot:c.length>0,shouldBlink:e,children:t},c.length))};if(e){const t=l?"submit form":"apply";if("multiline"===a){const e=(0,r.Ge)()?"\u2318":"Ctrl";h("Press ".concat(e,"+Enter to ").concat(t))}else"single"===a&&h("Press Enter to ".concat(t))}return s&&("chat"!==a||e)&&h("".concat(i.length,"/").concat(s),e&&i.length>=s),(0,u.jsx)(o.X7,{"data-testid":"InputInstructions",className:n,children:c})}},87814:(t,e,i)=>{i.d(e,{K:()=>r});var s=i(50641);class r{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(t,e,i){null!=this.formClearListener&&this.lastWidgetMgr===t&&this.lastFormId===e||(this.disconnect(),(0,s.bM)(e)&&(this.formClearListener=t.addFormClearedListener(e,i),this.lastWidgetMgr=t,this.lastFormId=e))}disconnect(){var t;null===(t=this.formClearListener)||void 0===t||t.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}},67175:(t,e,i)=>{i.r(e),i.d(e,{default:()=>k});var s=i(66845),r=i(20607),o=i(89997),n=i(25621),a=i(52347),l=i(87814),d=i(23849),u=i(16295),c=i(48266),h=i(8879),m=i(68411),p=i(46927),g=i(82534),b=i(79986),f=i(98478),v=i(86659),y=i(50641),x=i(1515);const I=(0,x.Z)("div",{target:"e116k4er3"})((t=>{let{theme:e}=t;return{display:"flex",flexDirection:"row",flexWrap:"nowrap",alignItems:"center",borderWidth:"1px",borderStyle:"solid",borderColor:e.colors.widgetBorderColor||e.colors.widgetBackgroundColor||e.colors.bgColor,transitionDuration:"200ms",transitionProperty:"border",transitionTimingFunction:"cubic-bezier(0.2, 0.8, 0.4, 1)",borderRadius:e.radii.lg,overflow:"hidden","&.focused":{borderColor:e.colors.primary},input:{MozAppearance:"textfield","&::-webkit-inner-spin-button, &::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:e.spacing.none}}}}),""),C=(0,x.Z)("div",{target:"e116k4er2"})({name:"76z9jo",styles:"display:flex;flex-direction:row;align-self:stretch"}),V=(0,x.Z)("button",{target:"e116k4er1"})((t=>{let{theme:e}=t;return{margin:e.spacing.none,border:"none",height:e.sizes.full,display:"flex",alignItems:"center",width:"".concat(32,"px"),justifyContent:"center",color:e.colors.bodyText,transition:"color 300ms, backgroundColor 300ms",backgroundColor:e.colors.widgetBackgroundColor||e.colors.secondaryBg,"&:hover:enabled, &:focus:enabled":{color:e.colors.white,backgroundColor:e.colors.primary,transition:"none",outline:"none"},"&:active":{outline:"none",border:"none"},"&:last-of-type":{borderTopRightRadius:e.radii.lg,borderBottomRightRadius:e.radii.lg},"&:disabled":{cursor:"not-allowed",color:e.colors.fadedText40}}}),""),w=(0,x.Z)("div",{target:"e116k4er0"})((t=>{let{theme:e,clearable:i}=t;return{position:"absolute",marginRight:e.spacing.twoXS,left:0,right:"".concat(64+(i?12:0),"px")}}),"");var F=i(40864);class D extends s.PureComponent{constructor(t){super(t),this.formClearHelper=new l.K,this.inputRef=s.createRef(),this.formatValue=t=>{if((0,y.le)(t))return null;const e=function(t){return null==t||""===t?void 0:t}(this.props.element.format);if(null==e)return t.toString();try{return(0,a.sprintf)(e,t)}catch(i){return(0,d.KE)("Error in sprintf(".concat(e,", ").concat(t,"): ").concat(i)),String(t)}},this.isIntData=()=>this.props.element.dataType===u.Y2.DataType.INT,this.getMin=()=>this.props.element.hasMin?this.props.element.min:-1/0,this.getMax=()=>this.props.element.hasMax?this.props.element.max:1/0,this.getStep=()=>{const{step:t}=this.props.element;return t||(this.isIntData()?1:.01)},this.commitWidgetValue=t=>{const{value:e}=this.state,{element:i,widgetMgr:s}=this.props,r=this.props.element,o=this.getMin(),n=this.getMax();if((0,y.bb)(e)&&(o>e||e>n)){const t=this.inputRef.current;t&&t.reportValidity()}else{var a;const o=null!==(a=null!==e&&void 0!==e?e:r.default)&&void 0!==a?a:null;this.isIntData()?s.setIntValue(i,o,t):s.setDoubleValue(i,o,t),this.setState({dirty:!1,value:o,formattedValue:this.formatValue(o)})}},this.onFormCleared=()=>{this.setState(((t,e)=>{var i;return{value:null!==(i=e.element.default)&&void 0!==i?i:null}}),(()=>this.commitWidgetValue({fromUi:!0})))},this.onBlur=()=>{this.state.dirty&&this.commitWidgetValue({fromUi:!0}),this.setState({isFocused:!1})},this.onFocus=()=>{this.setState({isFocused:!0})},this.onChange=t=>{const{value:e}=t.target;if(""===e)this.setState({dirty:!0,value:null,formattedValue:null});else{let t;t=this.isIntData()?parseInt(e,10):parseFloat(e),this.setState({dirty:!0,value:t,formattedValue:e})}},this.onKeyDown=t=>{const{key:e}=t;switch(e){case"ArrowUp":t.preventDefault(),this.modifyValueUsingStep("increment")();break;case"ArrowDown":t.preventDefault(),this.modifyValueUsingStep("decrement")()}},this.onKeyPress=t=>{"Enter"===t.key&&(this.state.dirty&&this.commitWidgetValue({fromUi:!0}),(0,y.$b)(this.props.element)&&this.props.widgetMgr.submitForm(this.props.element.formId))},this.modifyValueUsingStep=t=>()=>{const{value:e}=this.state,i=this.getStep();switch(t){case"increment":this.canIncrement&&this.setState({dirty:!0,value:(null!==e&&void 0!==e?e:this.getMin())+i},(()=>{this.commitWidgetValue({fromUi:!0})}));break;case"decrement":this.canDecrement&&this.setState({dirty:!0,value:(null!==e&&void 0!==e?e:this.getMax())-i},(()=>{this.commitWidgetValue({fromUi:!0})}))}},this.state={dirty:!1,value:this.initialValue,formattedValue:this.formatValue(this.initialValue),isFocused:!1}}get initialValue(){var t;const e=this.isIntData()?this.props.widgetMgr.getIntValue(this.props.element):this.props.widgetMgr.getDoubleValue(this.props.element);return null!==(t=null!==e&&void 0!==e?e:this.props.element.default)&&void 0!==t?t:null}componentDidMount(){this.props.element.setValue?this.updateFromProtobuf():this.commitWidgetValue({fromUi:!1})}componentDidUpdate(){this.maybeUpdateFromProtobuf()}componentWillUnmount(){this.formClearHelper.disconnect()}maybeUpdateFromProtobuf(){const{setValue:t}=this.props.element;t&&this.updateFromProtobuf()}updateFromProtobuf(){const{value:t}=this.props.element;this.props.element.setValue=!1,this.setState({value:null!==t&&void 0!==t?t:null,formattedValue:this.formatValue(null!==t&&void 0!==t?t:null)},(()=>{this.commitWidgetValue({fromUi:!1})}))}get canDecrement(){return!(0,y.le)(this.state.value)&&this.state.value-this.getStep()>=this.getMin()}get canIncrement(){return!(0,y.le)(this.state.value)&&this.state.value+this.getStep()<=this.getMax()}render(){var t;const{element:e,width:i,disabled:s,widgetMgr:n,theme:a}=this.props,{formattedValue:l,dirty:d,isFocused:u}=this.state,x={width:i},D=!this.canDecrement||s,k=!this.canIncrement||s,S=(0,y.le)(e.default)&&!s;return this.formClearHelper.manageFormClearListener(n,e.formId,this.onFormCleared),(0,F.jsxs)("div",{className:"stNumberInput",style:x,"data-testid":"stNumberInput",children:[(0,F.jsx)(f.O,{label:e.label,disabled:s,labelVisibility:(0,y.iF)(null===(t=e.labelVisibility)||void 0===t?void 0:t.value),children:e.help&&(0,F.jsx)(v.dT,{children:(0,F.jsx)(h.Z,{content:e.help,placement:m.u.TOP_RIGHT})})}),(0,F.jsxs)(I,{className:u?"focused":"","data-testid":"stNumberInputContainer",children:[(0,F.jsx)(g.Z,{type:"number",inputRef:this.inputRef,value:null!==l&&void 0!==l?l:"",placeholder:e.placeholder,onBlur:this.onBlur,onFocus:this.onFocus,onChange:this.onChange,onKeyPress:this.onKeyPress,onKeyDown:this.onKeyDown,clearable:S,clearOnEscape:S,disabled:s,"aria-label":e.label,overrides:{ClearIcon:{props:{overrides:{Svg:{style:{color:a.colors.darkGray,transform:"scale(1.4)",width:a.spacing.twoXL,marginRight:"-1.25em",":hover":{fill:a.colors.bodyText}}}}}},Input:{props:{"data-testid":"stNumberInput-Input",step:this.getStep(),min:this.getMin(),max:this.getMax()},style:{lineHeight:"1.4",paddingRight:".5rem",paddingLeft:".5rem",paddingBottom:".5rem",paddingTop:".5rem"}},InputContainer:{style:()=>({borderTopRightRadius:0,borderBottomRightRadius:0})},Root:{style:()=>({borderTopRightRadius:0,borderBottomRightRadius:0,borderLeftWidth:0,borderRightWidth:0,borderTopWidth:0,borderBottomWidth:0})}}}),i>c.A.hideNumberInputControls&&(0,F.jsxs)(C,{children:[(0,F.jsx)(V,{className:"step-down","data-testid":"stNumberInput-StepDown",onClick:this.modifyValueUsingStep("decrement"),disabled:D,tabIndex:-1,children:(0,F.jsx)(p.Z,{content:r.W,size:"xs",color:this.canDecrement?"inherit":"disabled"})}),(0,F.jsx)(V,{className:"step-up","data-testid":"stNumberInput-StepUp",onClick:this.modifyValueUsingStep("increment"),disabled:k,tabIndex:-1,children:(0,F.jsx)(p.Z,{content:o.v,size:"xs",color:this.canIncrement?"inherit":"disabled"})})]})]}),i>c.A.hideWidgetDetails&&(0,F.jsx)(w,{clearable:S,children:(0,F.jsx)(b.Z,{dirty:d,value:null!==l&&void 0!==l?l:"",className:"input-instructions",inForm:(0,y.$b)({formId:e.formId})})})]})}}const k=(0,n.b)(D)}}]);