"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[178],{178:(e,t,r)=>{r.r(t),r.d(t,{default:()=>c});var a=r(66845),s=r(16295),n=r(40864);const i=528;function c(e){let{element:t,width:r,endpoints:c}=e;const d=(0,a.useRef)(null),{type:l,url:o,startTime:u}=t;(0,a.useEffect)((()=>{d.current&&(d.current.currentTime=u)}),[u]),(0,a.useEffect)((()=>{const e=d.current,r=()=>{e&&(e.currentTime=t.startTime)};return e&&e.addEventListener("loadedmetadata",r),()=>{e&&e.removeEventListener("loadedmetadata",r)}}),[t]);const m=e=>{const{startTime:r}=t;return r?"".concat(e,"?start=").concat(r):e};if(l===s.nk.Type.YOUTUBE_IFRAME){const e=0!==r?.75*r:i;return(0,n.jsx)("iframe",{"data-testid":"stVideo",title:o,src:m(o),width:r,height:e,style:{colorScheme:"light dark"},frameBorder:"0",allow:"autoplay; encrypted-media",allowFullScreen:!0})}return(0,n.jsx)("video",{"data-testid":"stVideo",ref:d,controls:!0,src:c.buildMediaURL(o),className:"stVideo",style:{width:r,height:0===r?i:void 0}})}}}]);